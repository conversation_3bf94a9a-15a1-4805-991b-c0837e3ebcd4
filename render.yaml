# Render.com Configuration for SkillSwap
services:
  - type: web
    name: skillswap
    env: python
    plan: free
    buildCommand: pip install -r requirements.txt
    startCommand: streamlit run skillswap_simple.py --server.port=$PORT --server.address=0.0.0.0 --server.headless=true --server.enableCORS=false --server.enableXsrfProtection=false
    healthCheckPath: /_stcore/health
    envVars:
      - key: PYTHON_VERSION
        value: 3.11.0
      - key: STREAMLIT_SERVER_PORT
        value: $PORT
      - key: STREAMLIT_SERVER_ADDRESS
        value: 0.0.0.0
      - key: STREAMLIT_SERVER_HEADLESS
        value: true
