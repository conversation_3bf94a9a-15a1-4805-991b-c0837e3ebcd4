import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import openai
import pandas as pd

# API Keys
openai.api_key = st.secrets["OPENAI_API_KEY"]

# Google Sheets Auth
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(st.secrets["gcp_service_account"], scope)
client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Page State
if "submitted" not in st.session_state:
    st.session_state.submitted = False
if "user_skill" not in st.session_state:
    st.session_state.user_skill = None

# Page 1: Form
if not st.session_state.submitted:
    st.title("SkillSwap: Share What You Know, Learn What You Love")

    with st.form("skill_form"):
        name = st.text_input("Your Name")
        email = st.text_input("Your Email")
        skill_have = st.text_input("Skill You Can Teach")
        skill_want = st.text_input("Skill You Want to Learn")
        availability = st.text_input("Availability (e.g., Weekends, Evenings)")
        submitted = st.form_submit_button("Submit")

        if submitted:
            # Generate Bio
            prompt = f"Write a 2-sentence intro for someone who can teach {skill_have} and wants to learn {skill_want}."
            response = openai.Completion.create(
                engine="text-davinci-003", prompt=prompt, max_tokens=50
            )
            bio = response.choices[0].text.strip()

            # Save to Sheet
            sheet.append_row([
                datetime.now().isoformat(), name, email, skill_have, skill_want,
                availability, bio, "No", "No"
            ])

            st.session_state.submitted = True
            st.session_state.user_skill = skill_have.lower()
            st.success("Submitted! Taking you to your match list...")

            st.experimental_rerun()  # Reload to next page

# Page 2: Match Viewer
else:
    st.title("🎯 People Who Want to Learn What You Know")

    # Get all data
    records = sheet.get_all_records()
    df = pd.DataFrame(records)

    skill_you_have = st.session_state.user_skill

    # Find those who want your skill
    potential_learners = df[df["Skill_Want"].str.lower() == skill_you_have]

    if potential_learners.empty:
        st.info("No matches yet. Check back later!")
    else:
        for _, row in potential_learners.iterrows():
            st.markdown(f"""
            **Name**: {row['Name']}  
            **Skill Wanted**: {row['Skill_Want']}  
            **Availability**: {row['Availability']}  
            **Email**: {row['Email']}  
            **Bio**: _{row['Bio']}_  
            """)
            st.markdown("---")

    st.button("Back to Form", on_click=lambda: st.session_state.update({"submitted": False}))
