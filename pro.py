import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import pandas as pd

# Custom Theme Color Palette
COLORS = {
    "primary": "#2F80ED",        # Primary CTA color
    "secondary": "#27AE60",      # Secondary actions
    "background": "#F9FAFB",     # Background
    "card_bg": "#FFFFFF",        # Card components
    "text_primary": "#2D3748",   # Primary text
    "text_secondary": "#718096", # Secondary text
    "text_muted": "#A0ADB8",     # Muted text
    "gradient_start": "#2F80ED",
    "gradient_end": "#27AE60",
    "shadow": "rgba(0, 0, 0, 0.1)",
    "shadow_hover": "rgba(47, 128, 237, 0.2)"
}

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom Theme CSS Styling
def load_premium_css():
    st.markdown(f"""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@300;400;500;600&family=Open+Sans:wght@300;400;500;600&display=swap');

    /* Global Styles */
    .main {{
        font-family: 'Inter', sans-serif;
        background: {COLORS['background']};
        color: {COLORS['text_primary']};
    }}

    /* Hide Streamlit elements */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    .stDeployButton {{visibility: hidden;}}

    /* Custom Header with Linear Gradient */
    .premium-header {{
        background: linear-gradient(90deg, {COLORS['gradient_start']}, {COLORS['gradient_end']});
        padding: 3rem 2rem;
        color: white;
        text-align: center;
        margin-bottom: 3rem;
        box-shadow: 0 4px 20px {COLORS['shadow']};
    }}

    .premium-header h1 {{
        font-family: 'Poppins', sans-serif;
        font-size: 3rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}

    .premium-header p {{
        font-family: 'Inter', sans-serif;
        font-size: 1.2rem;
        opacity: 0.95;
        margin: 0;
        font-weight: 400;
    }}

    /* Centered Content Container */
    .hero-container {{
        max-width: 1000px;
        margin: 0 auto;
        padding: 0 2rem;
    }}

    /* Value Proposition Section */
    .value-prop {{
        text-align: center;
        margin-bottom: 3rem;
    }}

    .value-prop h2 {{
        font-family: 'Poppins', sans-serif;
        font-size: 2.8rem;
        font-weight: 700;
        color: {COLORS['text_primary']};
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }}

    .value-prop p {{
        font-family: 'Inter', sans-serif;
        font-size: 1.2rem;
        color: {COLORS['text_secondary']};
        margin-bottom: 2.5rem;
        line-height: 1.6;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
    }}

    /* Card Components */
    .card {{
        background: {COLORS['card_bg']};
        border-radius: 12px;
        box-shadow: 0 2px 8px {COLORS['shadow']};
        transition: all 0.3s ease;
    }}

    .card:hover {{
        box-shadow: 0 4px 16px {COLORS['shadow_hover']};
        transform: translateY(-2px);
    }}

    /* Trust Badges */
    .trust-badges {{
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin: 3rem 0;
        flex-wrap: wrap;
    }}

    .trust-badge {{
        background: {COLORS['card_bg']};
        color: {COLORS['primary']};
        padding: 0.8rem 1.5rem;
        border-radius: 8px;
        font-family: 'Inter', sans-serif;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 2px 8px {COLORS['shadow']};
        border: 1px solid rgba(47, 128, 237, 0.1);
        transition: all 0.3s ease;
    }}

    .trust-badge:hover {{
        transform: translateY(-2px);
        box-shadow: 0 4px 16px {COLORS['shadow_hover']};
        background: {COLORS['primary']};
        color: white;
    }}

    /* CTA Section */
    .cta-section {{
        background: {COLORS['card_bg']};
        border-radius: 12px;
        padding: 3rem 2rem;
        margin: 3rem auto;
        max-width: 600px;
        text-align: center;
        box-shadow: 0 4px 16px {COLORS['shadow']};
    }}

    .cta-title {{
        font-family: 'Poppins', sans-serif;
        font-size: 2.2rem;
        font-weight: 700;
        color: {COLORS['text_primary']};
        margin-bottom: 1rem;
    }}

    .cta-subtitle {{
        font-family: 'Inter', sans-serif;
        font-size: 1.1rem;
        color: {COLORS['text_secondary']};
        margin-bottom: 2.5rem;
        line-height: 1.5;
    }}

    /* Enhanced Buttons */
    .button-container {{
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        margin: 2rem 0;
        flex-wrap: wrap;
    }}

    .premium-button {{
        background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
        color: white;
        padding: 1.2rem 2.5rem;
        border: none;
        border-radius: 16px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.4s ease;
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        min-width: 200px;
        position: relative;
        overflow: hidden;
    }}

    .premium-button::before {{
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }}

    .premium-button:hover {{
        transform: translateY(-4px);
        box-shadow: 0 16px 35px rgba(37, 99, 235, 0.4);
        background: linear-gradient(135deg, {COLORS['secondary']}, {COLORS['primary']});
    }}

    .premium-button:hover::before {{
        left: 100%;
    }}

    .premium-button.secondary {{
        background: {COLORS['card_bg']};
        color: {COLORS['primary']};
        border: 2px solid {COLORS['primary']};
        box-shadow: 0 4px 15px {COLORS['shadow']};
    }}

    .premium-button.secondary:hover {{
        background: {COLORS['primary']};
        color: white;
        box-shadow: 0 12px 30px {COLORS['shadow_hover']};
    }}

    /* Custom Button Styling - 8px rounded corners, icon + label */
    .stButton > button {{
        padding: 1rem 2rem !important;
        border-radius: 8px !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 600 !important;
        font-size: 1rem !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        width: 100% !important;
        height: auto !important;
        min-height: 48px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        gap: 0.5rem !important;
    }}

    /* Primary Button - Start Your Journey */
    div[data-testid="column"]:first-child .stButton > button {{
        background: {COLORS['primary']} !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 2px 8px {COLORS['shadow']} !important;
    }}

    div[data-testid="column"]:first-child .stButton > button:hover {{
        background: #1C64D1 !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(47, 128, 237, 0.4) !important;
    }}

    /* Secondary Button - Login */
    div[data-testid="column"]:last-child .stButton > button {{
        background: transparent !important;
        color: {COLORS['primary']} !important;
        border: 2px solid {COLORS['primary']} !important;
        box-shadow: 0 2px 8px rgba(47, 128, 237, 0.2) !important;
    }}

    div[data-testid="column"]:last-child .stButton > button:hover {{
        background: #E6F0FF !important;
        color: {COLORS['primary']} !important;
        border: 2px solid {COLORS['primary']} !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px rgba(47, 128, 237, 0.3) !important;
    }}

    /* Default button styling (fallback) */
    .stButton > button:not([data-testid*="start"]):not([data-testid*="login"]) {{
        background: {COLORS['primary']} !important;
        color: white !important;
        border: none !important;
        box-shadow: 0 2px 8px {COLORS['shadow']} !important;
    }}

    .stButton > button:not([data-testid*="start"]):not([data-testid*="login"]):hover {{
        background: {COLORS['secondary']} !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 16px {COLORS['shadow_hover']} !important;
    }}

    .stButton > button:active {{
        transform: translateY(0) !important;
        box-shadow: 0 2px 8px {COLORS['shadow']} !important;
    }}

    /* Testimonials Section */
    .testimonials-section {{
        margin: 3rem 0;
        text-align: center;
    }}

    .testimonials-title {{
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 700;
        color: {COLORS['text_primary']};
        margin-bottom: 2rem;
    }}

    .testimonial-card {{
        background: {COLORS['card_bg']};
        padding: 2rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px {COLORS['shadow']};
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }}

    .testimonial-card:hover {{
        box-shadow: 0 4px 16px {COLORS['shadow_hover']};
        transform: translateY(-2px);
    }}

    .testimonial-text {{
        font-family: 'Inter', sans-serif;
        font-style: italic;
        color: {COLORS['text_secondary']};
        line-height: 1.6;
        margin-bottom: 1rem;
    }}

    .testimonial-author {{
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        color: {COLORS['text_primary']};
    }}

    .testimonial-role {{
        font-family: 'Inter', sans-serif;
        color: {COLORS['text_muted']};
        font-size: 0.9rem;
    }}

    /* Statistics Cards */
    .stats-grid {{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 3rem 0;
    }}

    .stat-card {{
        background: {COLORS['card_bg']};
        padding: 2rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 2px 8px {COLORS['shadow']};
        transition: all 0.3s ease;
    }}

    .stat-card:hover {{
        box-shadow: 0 4px 16px {COLORS['shadow_hover']};
        transform: translateY(-2px);
    }}

    .stat-number {{
        font-family: 'Poppins', sans-serif;
        font-size: 2.5rem;
        font-weight: 700;
        color: {COLORS['primary']};
        margin-bottom: 0.5rem;
        display: block;
    }}

    .stat-label {{
        font-family: 'Inter', sans-serif;
        color: {COLORS['text_secondary']};
        font-size: 0.9rem;
        font-weight: 500;
    }}

    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .premium-header {{
            padding: 2rem 1rem;
        }}

        .premium-header h1 {{
            font-size: 2.2rem;
        }}

        .value-prop h2 {{
            font-size: 2rem;
        }}

        .value-prop p {{
            font-size: 1rem;
        }}

        .trust-badges {{
            gap: 1rem;
        }}

        .trust-badge {{
            font-size: 0.85rem;
            padding: 0.6rem 1rem;
        }}

        .cta-section {{
            margin: 2rem 1rem;
            padding: 2rem 1.5rem;
        }}

        .cta-title {{
            font-size: 1.8rem;
        }}

        .stats-grid {{
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }}

        .testimonials-title {{
            font-size: 1.6rem;
        }}

        .testimonial-card {{
            padding: 1.5rem;
        }}

        .stButton > button {{
            font-size: 0.9rem !important;
            padding: 0.8rem 1.5rem !important;
        }}
    }}
    </style>
    """, unsafe_allow_html=True)

# Setup Google Sheets
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(
    st.secrets["gcp_service_account"], scope
)
client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Initialize session state
if "submitted" not in st.session_state:
    st.session_state.submitted = False
if "page" not in st.session_state:
    st.session_state.page = "landing"

# Premium Landing Page
def show_landing_page():
    # Premium Header
    st.markdown(f"""
    <div class="premium-header">
        <h1>SkillSwap</h1>
        <p>Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)

    # Hero Container
    st.markdown('<div class="hero-container">', unsafe_allow_html=True)

    # Value Proposition
    st.markdown(f"""
    <div class="value-prop">
        <h2>Exchange Skills.<br>Build Your Career.<br>Grow Your Network.</h2>
        <p>Join 3,200+ professionals who are teaching what they know and learning what they need.
        Quality matches, verified members, guaranteed results.</p>
    </div>
    """, unsafe_allow_html=True)

    # Trust Badges
    st.markdown(f"""
    <div class="trust-badges">
        <div class="trust-badge">✓ Verified Community</div>
        <div class="trust-badge">✓ Perfect Matches</div>
        <div class="trust-badge">✓ Direct Contact</div>
    </div>
    """, unsafe_allow_html=True)

    # CTA Section
    st.markdown(f"""
    <div class="cta-section">
        <h3 class="cta-title">Choose Your Path</h3>
        <p class="cta-subtitle">Join thousands of professionals exchanging skills and growing together</p>
    </div>
    """, unsafe_allow_html=True)

    # Side-by-side Buttons
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Create two columns for side-by-side buttons
        btn_col1, btn_col2 = st.columns(2)

        with btn_col1:
            st.markdown('<div class="primary-btn-container">', unsafe_allow_html=True)
            if st.button("🚀 Start Your Journey", key="start_journey", help="New to SkillSwap? Sign up here!", use_container_width=True):
                st.session_state.page = "register"
                st.rerun()
            st.markdown('</div>', unsafe_allow_html=True)

        with btn_col2:
            st.markdown('<div class="secondary-btn-container">', unsafe_allow_html=True)
            if st.button("👤 Login", key="login", help="Already have an account? Login here!", use_container_width=True):
                st.session_state.page = "login"
                st.rerun()
            st.markdown('</div>', unsafe_allow_html=True)

    # Social Proof Section
    show_social_proof()

    st.markdown('</div>', unsafe_allow_html=True)

# Social Proof Section with Custom Theme
def show_social_proof():
    # Statistics Grid
    st.markdown(f"""
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">3,247</span>
            <div class="stat-label">Active Learners</div>
        </div>
        <div class="stat-card">
            <span class="stat-number" style="color: {COLORS['secondary']};">1,856</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">92%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    # Testimonials Section
    st.markdown(f"""
    <div class="testimonials-section">
        <h3 class="testimonials-title">What Our Community Says</h3>
    </div>
    """, unsafe_allow_html=True)

    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months!",
            "author": "Rahul Kumar",
            "role": "Software Developer"
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer"
        },
        {
            "text": "Worth every rupee. The quality of matches is outstanding and the platform is easy to use.",
            "author": "Arjun Singh",
            "role": "Data Analyst"
        }
    ]

    cols = st.columns(3)
    for i, testimonial in enumerate(testimonials):
        with cols[i]:
            st.markdown(f"""
            <div class="testimonial-card">
                <p class="testimonial-text">"{testimonial['text']}"</p>
                <div class="testimonial-author">{testimonial['author']}</div>
                <div class="testimonial-role">{testimonial['role']}</div>
                <div style="color: {COLORS['primary']}; margin-top: 0.5rem;">★★★★★</div>
            </div>
            """, unsafe_allow_html=True)

# Registration Page
def show_registration_page():
    if st.button("← Back to Home", key="back_home"):
        st.session_state.page = "landing"
        st.rerun()

    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h2 style="color: {COLORS['primary']}; font-size: 2.5rem;">Join SkillSwap Community</h2>
        <p style="color: {COLORS['text_secondary']}; font-size: 1.2rem;">Tell us about your skills and start learning</p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("skill_form"):
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input("Your Name", placeholder="e.g., Priya Sharma")
            email = st.text_input("Email Address", placeholder="<EMAIL>")
            skill_have = st.text_input("Skill you can teach", placeholder="e.g., React Development")

        with col2:
            skill_want = st.text_input("Skill you want to learn", placeholder="e.g., UI/UX Design")
            availability = st.selectbox("Availability",
                                      ["Weekends Only", "Weekday Evenings", "Flexible Schedule"])
            commitment = st.selectbox("Time Commitment",
                                    ["1-2 hours/week", "3-5 hours/week", "6+ hours/week"])

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button("Find My Matches", use_container_width=True)

        if submitted:
            if name and email and skill_have and skill_want:
                # Generate simple bio
                bio = f"Hi! I'm {name}. I can teach {skill_have} and want to learn {skill_want}."

                # Add to Google Sheets
                sheet.append_row([
                    datetime.now().isoformat(),
                    name, email, skill_have, skill_want, availability, bio, "No", "No"
                ])
                st.session_state.submitted = True
                st.session_state.page = "matches"
                st.rerun()
            else:
                st.error("Please fill in all fields")

# Login Page
def show_login_page():
    if st.button("← Back to Home", key="back_home_login"):
        st.session_state.page = "landing"
        st.rerun()

    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h2 style="color: {COLORS['primary']}; font-size: 2.5rem;">Welcome Back!</h2>
        <p style="color: {COLORS['text_secondary']}; font-size: 1.2rem;">Enter your email to access your matches</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        with st.form("login_form"):
            email = st.text_input("Email Address", placeholder="Enter your registered email")
            submitted = st.form_submit_button("Access My Matches", use_container_width=True)

            if submitted:
                if email and "@" in email:
                    st.session_state.submitted = True
                    st.session_state.page = "matches"
                    st.rerun()
                else:
                    st.error("Please enter a valid email address.")

# Matches Page
def show_matches_page():
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h2 style="color: {COLORS['primary']}; font-size: 2.5rem;">🎉 Your Perfect Matches!</h2>
        <p style="color: {COLORS['text_secondary']}; font-size: 1.2rem;">Here are people who want to learn what you teach</p>
    </div>
    """, unsafe_allow_html=True)

    # Get all data from sheet
    try:
        data = sheet.get_all_records()
        df = pd.DataFrame(data)

        if len(df) > 1:
            # Show other users (excluding current user)
            for _, user in df.iloc[:-1].iterrows():
                st.markdown(f"""
                <div style="background: {COLORS['white']}; padding: 2rem; border-radius: 16px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); margin-bottom: 1.5rem;">
                    <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                        <div style="width: 60px; height: 60px; border-radius: 50%; background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
                             display: flex; align-items: center; justify-content: center; color: white; font-weight: 600; margin-right: 1.5rem; font-size: 1.2rem;">
                            {user.get('Name', 'U')[0].upper()}
                        </div>
                        <div>
                            <h3 style="color: {COLORS['text_primary']}; margin: 0;">{user.get('Name', 'Unknown')}</h3>
                            <p style="color: {COLORS['text_secondary']}; margin: 0;">Available: {user.get('Availability', 'Not specified')}</p>
                        </div>
                    </div>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                        <div>
                            <strong style="color: {COLORS['secondary']};">Can Teach:</strong><br>
                            <span style="color: {COLORS['text_primary']};">{user.get('Skill_Have', 'Not specified')}</span>
                        </div>
                        <div>
                            <strong style="color: {COLORS['primary']};">Wants to Learn:</strong><br>
                            <span style="color: {COLORS['text_primary']};">{user.get('Skill_Want', 'Not specified')}</span>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("You're the first user! Share this with friends to find matches.")
    except Exception as e:
        st.error(f"Error loading matches: {str(e)}")

    if st.button("← Back to Home", key="back_to_home"):
        st.session_state.page = "landing"
        st.rerun()

def main():
    load_premium_css()

    # Page routing
    if st.session_state.page == "landing":
        show_landing_page()
    elif st.session_state.page == "register":
        show_registration_page()
    elif st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "matches":
        show_matches_page()
    elif st.session_state.submitted:
        show_matches_page()

if __name__ == "__main__":
    main()

# PAGE 2: Show People Who Want to Learn Your Skill
st.title("People Who Want to Learn From You")

# Fetch all data - use fallback method to handle duplicate headers
try:
    # First, let's see what's actually in the sheet
    all_values = sheet.get_all_values()

    if len(all_values) > 1:
        # Get headers and make them unique
        headers = all_values[0]
        unique_headers = []
        header_counts = {}

        for header in headers:
            if header in header_counts:
                header_counts[header] += 1
                unique_headers.append(f"{header}_{header_counts[header]}")
            else:
                header_counts[header] = 0
                unique_headers.append(header)

        # Create DataFrame with unique headers
        df = pd.DataFrame(all_values[1:], columns=unique_headers)

        # Show debug info
        st.sidebar.write("**Debug Info:**")
        st.sidebar.write(f"Original headers: {headers}")
        st.sidebar.write(f"Unique headers: {unique_headers}")
        st.sidebar.write(f"Rows found: {len(df)}")

    else:
        df = pd.DataFrame()
        st.info("No data found in the spreadsheet yet.")

except Exception as e:
    st.error(f"Error loading data: {str(e)}")
    df = pd.DataFrame()

# Filter users who want the current user's skill
if not df.empty:
    # Find the correct column names (handle potential variations)
    skill_want_col = None
    email_col = None
    name_col = None
    availability_col = None

    for col in df.columns:
        if 'skill' in col.lower() and 'want' in col.lower():
            skill_want_col = col
        elif 'email' in col.lower():
            email_col = col
        elif 'name' in col.lower():
            name_col = col
        elif 'availability' in col.lower():
            availability_col = col

    if skill_want_col and email_col and name_col:
        # Filter users who want the current user's skill
        interested_users = df[df[skill_want_col].str.lower() == st.session_state.skill_have.lower()]

        if not interested_users.empty:
            for _, row in interested_users.iterrows():
                if row[email_col] != st.session_state.email:  # Exclude self
                    st.subheader(row[name_col])
                    st.write("📩 Email:", row[email_col])
                    st.write("✅ Wants to learn:", row[skill_want_col])
                    if availability_col:
                        st.write("📅 Availability:", row[availability_col])
                    st.markdown("---")
        else:
            st.info("No one is currently looking to learn your skill. Check back soon!")
    else:
        st.error("Could not find the required columns in the spreadsheet. Please check your Google Sheet structure.")
else:
    st.info("No data found in the spreadsheet yet.")

