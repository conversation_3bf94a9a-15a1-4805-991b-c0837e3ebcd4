import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import pandas as pd

# Setup Google Sheets
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(
    st.secrets["gcp_service_account"], scope
)
client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Initialize session state
if "submitted" not in st.session_state:
    st.session_state.submitted = False

if not st.session_state.submitted:
    # PAGE 1: Skill Entry Form
    st.title("SkillSwap: Exchange Skills for Free")

    with st.form("skill_form"):
        name = st.text_input("Your Name")
        email = st.text_input("Your Email")
        skill_have = st.text_input("Skill You Can Teach")
        skill_want = st.text_input("Skill You Want to Learn")
        availability = st.text_input("Availability (e.g., Weekends, Evenings)")
        submitted = st.form_submit_button("Submit")

        if submitted:
            # Generate simple bio
            bio = f"Hi! I'm {name}. I can teach {skill_have} and want to learn {skill_want}."

            # Append to Google Sheet
            sheet.append_row([
                datetime.now().isoformat(),
                name,
                email,
                skill_have,
                skill_want,
                availability,
                bio,
                "No",
                "No"
            ])

            # Store data in session
            st.session_state.name = name
            st.session_state.email = email
            st.session_state.skill_have = skill_have
            st.session_state.skill_want = skill_want
            st.session_state.submitted = True

            st.success("Thanks! You're now entering the SkillSwap network...")

    st.stop()

# PAGE 2: Show People Who Want to Learn Your Skill
st.title("People Who Want to Learn From You")

# Fetch all data
data = sheet.get_all_records()
df = pd.DataFrame(data)

# Filter users who want the current user's skill
interested_users = df[df["Skill_Want"].str.lower() == st.session_state.skill_have.lower()]

if not interested_users.empty:
    for _, row in interested_users.iterrows():
        if row["Email"] != st.session_state.email:  # Exclude self
            st.subheader(row["Name"])
            st.write("📩 Email:", row["Email"])
            st.write("✅ Wants to learn:", row["Skill_Want"])
            st.write("📅 Availability:", row["Availability"])
            st.markdown("---")
else:
    st.info("No one is currently looking to learn your skill. Check back soon!")

