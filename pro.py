import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
from openai import OpenAI

# API Keys from secrets.toml
client_openai = OpenAI(api_key=st.secrets["OPENAI_API_KEY"])

# Google Sheets setup
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(
    st.secrets["gcp_service_account"], scope
)

client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Streamlit UI
st.title("SkillSwap: Exchange Skills for Free")

with st.form("skill_form"):
    name = st.text_input("Your Name")
    email = st.text_input("Your Email")
    skill_have = st.text_input("Skill You Can Teach")
    skill_want = st.text_input("Skill You Want to Learn")
    availability = st.text_input("Availability (e.g., Weekends, Evenings)")
    submitted = st.form_submit_button("Submit")

    if submitted:
        # AI-generated bio
        prompt = f"Write a 2-sentence introduction for someone who can teach {skill_have} and wants to learn {skill_want}."
        response = client_openai.completions.create(
            model="gpt-3.5-turbo-instruct", prompt=prompt, max_tokens=50
        )
        bio = response.choices[0].text.strip()

        # Append data to Google Sheet
        sheet.append_row([
            datetime.now().isoformat(),
            name,
            email,
            skill_have,
            skill_want,
            availability,
            bio,
            "No",
            "No"
        ])

        st.success("You're in! We'll notify you when you're matched.")
        st.write("Your AI-Generated Bio:")
        st.info(bio)
