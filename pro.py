import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import pandas as pd

# Setup Google Sheets
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(
    st.secrets["gcp_service_account"], scope
)
client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Initialize session state
if "submitted" not in st.session_state:
    st.session_state.submitted = False

if not st.session_state.submitted:
    # PAGE 1: Skill Entry Form
    st.title("SkillSwap: Exchange Skills for Free")

    with st.form("skill_form"):
        name = st.text_input("Your Name")
        email = st.text_input("Your Email")
        skill_have = st.text_input("Skill You Can Teach")
        skill_want = st.text_input("Skill You Want to Learn")
        availability = st.text_input("Availability (e.g., Weekends, Evenings)")
        submitted = st.form_submit_button("Submit")

        if submitted:
            # Generate simple bio
            bio = f"Hi! I'm {name}. I can teach {skill_have} and want to learn {skill_want}."

            # Append to Google Sheet
            sheet.append_row([
                datetime.now().isoformat(),
                name,
                email,
                skill_have,
                skill_want,
                availability,
                bio,
                "No",
                "No"
            ])

            # Store data in session
            st.session_state.name = name
            st.session_state.email = email
            st.session_state.skill_have = skill_have
            st.session_state.skill_want = skill_want
            st.session_state.submitted = True

            st.success("Thanks! You're now entering the SkillSwap network...")

    st.stop()

# PAGE 2: Show People Who Want to Learn Your Skill
st.title("People Who Want to Learn From You")

# Fetch all data with explicit headers to handle duplicates
try:
    # Define expected headers to avoid duplicate column issues
    expected_headers = [
        "Timestamp", "Name", "Email", "Skill_Have", "Skill_Want",
        "Availability", "Bio", "Matched", "Contacted"
    ]
    data = sheet.get_all_records(expected_headers=expected_headers)
    df = pd.DataFrame(data)
except Exception as e:
    # Fallback: get raw data and create DataFrame manually
    st.warning("Using fallback data loading method...")
    all_values = sheet.get_all_values()
    if len(all_values) > 1:
        # Use first row as headers, but make them unique
        headers = all_values[0]
        unique_headers = []
        for i, header in enumerate(headers):
            if header in unique_headers:
                unique_headers.append(f"{header}_{i}")
            else:
                unique_headers.append(header)

        df = pd.DataFrame(all_values[1:], columns=unique_headers)
    else:
        df = pd.DataFrame()

# Filter users who want the current user's skill
if not df.empty:
    # Find the correct column names (handle potential variations)
    skill_want_col = None
    email_col = None
    name_col = None
    availability_col = None

    for col in df.columns:
        if 'skill' in col.lower() and 'want' in col.lower():
            skill_want_col = col
        elif 'email' in col.lower():
            email_col = col
        elif 'name' in col.lower():
            name_col = col
        elif 'availability' in col.lower():
            availability_col = col

    if skill_want_col and email_col and name_col:
        # Filter users who want the current user's skill
        interested_users = df[df[skill_want_col].str.lower() == st.session_state.skill_have.lower()]

        if not interested_users.empty:
            for _, row in interested_users.iterrows():
                if row[email_col] != st.session_state.email:  # Exclude self
                    st.subheader(row[name_col])
                    st.write("📩 Email:", row[email_col])
                    st.write("✅ Wants to learn:", row[skill_want_col])
                    if availability_col:
                        st.write("📅 Availability:", row[availability_col])
                    st.markdown("---")
        else:
            st.info("No one is currently looking to learn your skill. Check back soon!")
    else:
        st.error("Could not find the required columns in the spreadsheet. Please check your Google Sheet structure.")
else:
    st.info("No data found in the spreadsheet yet.")

