import streamlit as st

# Page configuration
st.set_page_config(
    page_title="SkillSwap Payment Tester",
    page_icon="💳",
    layout="wide"
)

import razorpay
import json
from datetime import datetime

def main():
    st.title("💳 SkillSwap Payment Integration Tester")
    st.markdown("Test your Razorpay integration before going live!")
    
    # Check if API keys are configured
    try:
        key_id = st.secrets["RAZORPAY_KEY_ID"]
        key_secret = st.secrets["RAZORPAY_KEY_SECRET"]
        
        if "YOUR_KEY_ID_HERE" in key_id or "YOUR_SECRET_KEY_HERE" in key_secret:
            st.error("🔑 Please add your actual Razorpay API keys to .streamlit/secrets.toml")
            st.markdown("""
            ### How to get your API keys:
            1. Go to [razorpay.com](https://razorpay.com)
            2. Sign up and verify your account
            3. Go to Settings → API Keys
            4. Generate Test Keys
            5. Copy Key ID and Secret to your secrets.toml file
            """)
            return
        
        # Initialize Razorpay client
        client = razorpay.Client(auth=(key_id, key_secret))
        st.success("✅ Razorpay client initialized successfully!")
        
    except KeyError as e:
        st.error(f"❌ Missing configuration: {e}")
        st.info("Add RAZORPAY_KEY_ID and RAZORPAY_KEY_SECRET to your .streamlit/secrets.toml file")
        return
    except Exception as e:
        st.error(f"❌ Error initializing Razorpay: {str(e)}")
        return
    
    # Test payment link creation
    st.header("🔗 Test Payment Link Creation")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Customer Details")
        test_name = st.text_input("Customer Name", value="Test User")
        test_email = st.text_input("Customer Email", value="<EMAIL>")
        test_amount = st.number_input("Amount (₹)", min_value=1, value=49, step=1)
    
    with col2:
        st.subheader("Payment Configuration")
        currency = st.selectbox("Currency", ["INR", "USD"], index=0)
        description = st.text_input("Description", value="SkillSwap Premium Subscription - Monthly")
        callback_url = st.text_input("Callback URL", value="https://your-app.streamlit.app/payment-success")
    
    if st.button("🚀 Create Test Payment Link", type="primary"):
        try:
            # Create payment link
            payment_link_data = {
                "amount": test_amount * 100,  # Convert to paise
                "currency": currency,
                "description": description,
                "customer": {
                    "name": test_name,
                    "email": test_email
                },
                "notify": {
                    "sms": False,
                    "email": True
                },
                "reminder_enable": True,
                "callback_url": callback_url,
                "callback_method": "get"
            }
            
            with st.spinner("Creating payment link..."):
                payment_link = client.payment_link.create(payment_link_data)
            
            st.success("🎉 Payment link created successfully!")
            
            # Display payment link details
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("### 📋 Payment Link Details")
                st.json({
                    "id": payment_link["id"],
                    "amount": f"₹{payment_link['amount']/100}",
                    "currency": payment_link["currency"],
                    "status": payment_link["status"],
                    "created_at": datetime.fromtimestamp(payment_link["created_at"]).strftime("%Y-%m-%d %H:%M:%S")
                })
            
            with col2:
                st.markdown("### 🔗 Test Payment")
                st.markdown(f"**Payment URL**: [Click here to test payment]({payment_link['short_url']})")
                st.code(payment_link['short_url'])
                
                st.markdown("### 📱 QR Code")
                st.info("Scan this QR code to test mobile payment")
                # Note: QR code would be generated by Razorpay
            
            # Instructions
            st.markdown("---")
            st.markdown("### 🧪 How to Test")
            st.markdown("""
            1. **Click the payment link** above
            2. **Use test card details**:
               - Card: 4111 1111 1111 1111
               - CVV: Any 3 digits
               - Expiry: Any future date
            3. **Complete the payment**
            4. **Check if callback works**
            5. **Verify payment status** in Razorpay dashboard
            """)
            
        except Exception as e:
            st.error(f"❌ Failed to create payment link: {str(e)}")
            st.markdown("### Common Issues:")
            st.markdown("""
            - Check if your API keys are correct
            - Ensure you're using test keys (start with rzp_test_)
            - Verify your Razorpay account is active
            - Check if amount is valid (minimum ₹1)
            """)
    
    # Test other Razorpay features
    st.header("🔍 Additional Tests")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("📊 Test API Connection"):
            try:
                # Test API by fetching payment links
                payment_links = client.payment_link.all({"count": 1})
                st.success("✅ API connection working!")
                st.json({"status": "connected", "account_id": key_id[:12] + "..."})
            except Exception as e:
                st.error(f"❌ API connection failed: {str(e)}")
    
    with col2:
        if st.button("💰 Check Account Balance"):
            try:
                # Note: Balance API might not be available in test mode
                st.info("💡 Balance check available in live mode only")
            except Exception as e:
                st.error(f"❌ Balance check failed: {str(e)}")
    
    with col3:
        if st.button("📈 View Recent Payments"):
            try:
                payments = client.payment.all({"count": 5})
                st.success("✅ Recent payments fetched!")
                if payments['items']:
                    for payment in payments['items']:
                        st.json({
                            "id": payment["id"],
                            "amount": f"₹{payment['amount']/100}",
                            "status": payment["status"],
                            "method": payment.get("method", "N/A")
                        })
                else:
                    st.info("No payments found")
            except Exception as e:
                st.error(f"❌ Failed to fetch payments: {str(e)}")
    
    # Integration guide
    st.header("📚 Integration Guide")
    
    with st.expander("🔧 How to integrate with SkillSwap"):
        st.markdown("""
        ### Step 1: Update your secrets.toml
        ```toml
        RAZORPAY_KEY_ID = "your_actual_key_id"
        RAZORPAY_KEY_SECRET = "your_actual_secret"
        ```
        
        ### Step 2: Test the payment flow
        1. Create payment link (done above)
        2. Complete test payment
        3. Verify webhook/callback
        
        ### Step 3: Go live
        1. Complete KYC in Razorpay
        2. Generate live API keys
        3. Update secrets with live keys
        4. Test with real small amount
        
        ### Step 4: Monitor
        1. Check Razorpay dashboard
        2. Monitor payment success rates
        3. Handle failed payments
        4. Set up webhooks for automation
        """)
    
    # Success metrics
    st.header("📊 Expected Revenue")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Monthly Price", "₹49")
    with col2:
        st.metric("Target Users (Month 1)", "100")
    with col3:
        st.metric("Expected Premium (%)", "20%")
    with col4:
        st.metric("Monthly Revenue", "₹980")
    
    st.markdown("---")
    st.markdown("### 🎯 Next Steps")
    st.markdown("""
    1. ✅ **Test payment link** (click the link above)
    2. ✅ **Complete test payment** using test card
    3. ✅ **Verify in Razorpay dashboard**
    4. ✅ **Update SkillSwap app** with working payment
    5. ✅ **Launch and start earning!** 🚀
    """)

if __name__ == "__main__":
    main()
