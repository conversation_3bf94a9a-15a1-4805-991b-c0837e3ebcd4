import streamlit as st

# Page configuration - MUST BE FIRST
st.set_page_config(
    page_title="SkillSwap - Exchange Skills, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="expanded"
)

import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime, timedelta
import hashlib
import time

# Try to import optional dependencies
try:
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    import smtplib
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

try:
    import razorpay
    RAZORPAY_AVAILABLE = True
except ImportError:
    RAZORPAY_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Initialize session state
def init_session_state():
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "user_email" not in st.session_state:
        st.session_state.user_email = None
    if "is_premium" not in st.session_state:
        st.session_state.is_premium = False
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}

# Google Sheets setup
@st.cache_resource
def setup_google_sheets():
    try:
        scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
        creds = ServiceAccountCredentials.from_json_keyfile_dict(
            st.secrets["gcp_service_account"], scope
        )
        client = gspread.authorize(creds)
        
        # Setup sheets
        sheets = {
            'users': client.open("SkillSwap_Users").sheet1,
        }
        return sheets
    except Exception as e:
        st.error(f"Google Sheets setup failed: {str(e)}")
        return None

# Simple bio generation
def generate_simple_bio(skill_have, skill_want, name):
    return f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. Let's exchange skills and grow together!"

# User registration
def register_user(sheets, user_data):
    if not sheets:
        st.error("Google Sheets not available")
        return False
    
    try:
        sheets['users'].append_row([
            datetime.now().isoformat(),
            user_data['name'],
            user_data['email'],
            user_data['skill_have'],
            user_data['skill_want'],
            user_data['availability'],
            user_data['bio'],
            hashlib.sha256(user_data['email'].encode()).hexdigest(),
            "Free"
        ])
        return True
    except Exception as e:
        st.error(f"Registration failed: {str(e)}")
        return False

# Main application
def main():
    init_session_state()
    
    # Setup services
    sheets = setup_google_sheets()
    
    # Show dependency status
    if not EMAIL_AVAILABLE:
        st.sidebar.warning("📧 Email features disabled")
    if not RAZORPAY_AVAILABLE:
        st.sidebar.warning("💳 Payment features disabled")
    if not OPENAI_AVAILABLE:
        st.sidebar.warning("🤖 AI features disabled")
    
    # Sidebar navigation
    st.sidebar.title("🔄 SkillSwap")
    
    if not st.session_state.authenticated:
        page = st.sidebar.selectbox("Navigate", ["Home", "Register", "Login"])
    else:
        page = st.sidebar.selectbox("Navigate", ["Dashboard", "Profile", "Logout"])
    
    # Page routing
    if page == "Home":
        show_home_page()
    elif page == "Register":
        show_register_page(sheets)
    elif page == "Login":
        show_login_page()
    elif page == "Dashboard":
        show_dashboard()
    elif page == "Profile":
        show_profile_page()
    elif page == "Logout":
        logout()

def show_home_page():
    st.title("🔄 SkillSwap")
    st.subheader("Exchange Skills, Grow Together")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🎯 How It Works
        1. **Register** with your skills
        2. **Upgrade** to Premium (₹49/month)
        3. **Get matched** with skill partners
        4. **Exchange** knowledge and grow!
        
        ### ✨ Why SkillSwap?
        - **Curated Community**: Only serious learners
        - **Perfect Matches**: AI-powered matching
        - **Quality Assured**: Premium membership ensures commitment
        """)
    
    with col2:
        st.markdown("""
        ### 💎 Premium Benefits
        - Access to all skill matches
        - Priority matching algorithm
        - Direct contact information
        - AI-generated introductions
        - Email notifications for new matches
        
        ### 🚀 Popular Skills
        - Web Development
        - Digital Marketing
        - Graphic Design
        - Data Science
        - Language Learning
        """)
    
    st.markdown("---")
    st.markdown("### 🎉 Ready to Start Learning?")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("🔥 Register Now", type="primary"):
            st.session_state.page = "Register"
            st.rerun()
    
    with col2:
        if st.button("🔑 Login"):
            st.session_state.page = "Login"
            st.rerun()
    
    with col3:
        st.markdown("**Only ₹49/month**")

def show_register_page(sheets):
    st.title("🚀 Join SkillSwap")
    st.markdown("Create your account and start exchanging skills!")
    
    with st.form("registration_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("Full Name*", placeholder="Enter your full name")
            email = st.text_input("Email Address*", placeholder="<EMAIL>")
            skill_have = st.text_input("Skill You Can Teach*", placeholder="e.g., Python Programming")
        
        with col2:
            skill_want = st.text_input("Skill You Want to Learn*", placeholder="e.g., Digital Marketing")
            availability = st.selectbox("Availability*", [
                "Weekends Only",
                "Weekday Evenings",
                "Flexible Schedule",
                "Weekday Mornings",
                "Custom Schedule"
            ])
            custom_availability = st.text_input("Custom Availability (if selected)", placeholder="Specify your availability")
        
        terms_agreed = st.checkbox("I agree to the Terms of Service and Privacy Policy*")
        submitted = st.form_submit_button("Create Account", type="primary")
        
        if submitted:
            if not all([name, email, skill_have, skill_want, availability]):
                st.error("Please fill in all required fields marked with *")
            elif not terms_agreed:
                st.error("Please agree to the Terms of Service")
            elif "@" not in email:
                st.error("Please enter a valid email address")
            else:
                # Generate simple bio
                bio = generate_simple_bio(skill_have, skill_want, name)
                
                # Prepare user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability
                user_data = {
                    'name': name,
                    'email': email,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'availability': final_availability,
                    'bio': bio
                }
                
                # Register user
                if register_user(sheets, user_data):
                    st.success("🎉 Account created successfully!")
                    st.info("Your bio: " + bio)
                    st.markdown("### Next Steps:")
                    st.markdown("1. **Upgrade to Premium** to access matches")
                    st.markdown("2. **Get matched** with skill partners")
                    st.markdown("3. **Start learning** and teaching!")
                    
                    # Auto-login
                    st.session_state.authenticated = True
                    st.session_state.user_email = email
                    st.session_state.user_data = user_data
                    st.session_state.is_premium = False
                    
                    time.sleep(2)
                    st.rerun()

def show_login_page():
    st.title("🔑 Welcome Back")
    st.markdown("Login to your SkillSwap account")
    
    with st.form("login_form"):
        email = st.text_input("Email Address", placeholder="<EMAIL>")
        submitted = st.form_submit_button("Login", type="primary")
        
        if submitted:
            if not email:
                st.error("Please enter your email address")
            elif "@" not in email:
                st.error("Please enter a valid email address")
            else:
                # Simple login simulation
                st.session_state.authenticated = True
                st.session_state.user_email = email
                st.session_state.user_data = {"Name": "Demo User", "Email": email}
                st.session_state.is_premium = False
                
                st.success("Login successful!")
                time.sleep(1)
                st.rerun()

def show_dashboard():
    st.title(f"👋 Welcome back, {st.session_state.user_data.get('Name', 'User')}!")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.metric("Your Status", "Premium" if st.session_state.is_premium else "Free")
    
    with col2:
        st.metric("Available Matches", "Upgrade to Premium" if not st.session_state.is_premium else "0")
    
    with col3:
        st.metric("Skill Teaching", st.session_state.user_data.get('skill_have', 'N/A'))
    
    st.markdown("---")
    
    if not st.session_state.is_premium:
        st.markdown("### 🚀 Upgrade to Premium")
        st.warning("Upgrade to Premium to access skill matches and connect with other learners!")
        
        if st.button("💎 Upgrade Now - ₹49/month", type="primary"):
            # Create payment link
            try:
                if RAZORPAY_AVAILABLE:
                    key_id = st.secrets.get("RAZORPAY_KEY_ID", "")
                    key_secret = st.secrets.get("RAZORPAY_KEY_SECRET", "")

                    if "YOUR_KEY_ID_HERE" not in key_id and key_id:
                        client = razorpay.Client(auth=(key_id, key_secret))

                        payment_link = client.payment_link.create({
                            "amount": 4900,  # ₹49 in paise
                            "currency": "INR",
                            "description": "SkillSwap Premium Subscription - Monthly",
                            "customer": {
                                "name": st.session_state.user_data.get('Name', 'User'),
                                "email": st.session_state.user_email
                            },
                            "notify": {
                                "sms": False,
                                "email": True
                            },
                            "reminder_enable": True,
                            "callback_url": "https://your-app.streamlit.app/payment-success",
                            "callback_method": "get"
                        })

                        st.success("🎉 Payment link created!")
                        st.markdown(f"[💳 **Click here to pay ₹49**]({payment_link['short_url']})")
                        st.info("After payment, refresh this page to access premium features!")
                    else:
                        st.warning("⚠️ Payment system not configured. Add your Razorpay API keys to secrets.toml")
                else:
                    st.info("💡 Install razorpay package to enable payments: pip install razorpay")
            except Exception as e:
                st.error(f"Payment error: {str(e)}")
                st.info("Please check your Razorpay configuration")

def show_profile_page():
    st.title("👤 Your Profile")
    
    user_data = st.session_state.user_data
    
    st.markdown("### 📋 Current Information")
    st.markdown(f"**Name:** {user_data.get('Name', 'N/A')}")
    st.markdown(f"**Email:** {st.session_state.user_email}")
    st.markdown(f"**Status:** {'Premium' if st.session_state.is_premium else 'Free'}")

def logout():
    st.session_state.authenticated = False
    st.session_state.user_email = None
    st.session_state.is_premium = False
    st.session_state.user_data = {}
    st.success("Logged out successfully!")
    time.sleep(1)
    st.rerun()

if __name__ == "__main__":
    main()
