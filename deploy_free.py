#!/usr/bin/env python3
"""
SkillSwap Free Deployment Helper
One-click deployment to free hosting platforms
"""

import os
import sys
import subprocess
import json
import webbrowser
from pathlib import Path

def check_git():
    """Check if git is installed and repo is initialized"""
    try:
        subprocess.run(['git', '--version'], capture_output=True, check=True)
        
        # Check if in git repo
        result = subprocess.run(['git', 'status'], capture_output=True)
        if result.returncode != 0:
            print("📁 Initializing git repository...")
            subprocess.run(['git', 'init'], check=True)
            subprocess.run(['git', 'add', '.'], check=True)
            subprocess.run(['git', 'commit', '-m', 'Initial SkillSwap commit'], check=True)
        
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Git not found. Please install git first.")
        return False

def create_github_repo():
    """Guide user to create GitHub repository"""
    print("\n🐙 GitHub Repository Setup:")
    print("1. Go to https://github.com/new")
    print("2. Repository name: skillswap")
    print("3. Make it Public")
    print("4. Don't initialize with README (we have files)")
    print("5. Click 'Create repository'")
    
    repo_url = input("\n📝 Enter your GitHub repository URL (https://github.com/username/skillswap): ")
    
    if repo_url:
        try:
            subprocess.run(['git', 'remote', 'add', 'origin', repo_url], check=True)
            subprocess.run(['git', 'branch', '-M', 'main'], check=True)
            subprocess.run(['git', 'push', '-u', 'origin', 'main'], check=True)
            print("✅ Code pushed to GitHub!")
            return repo_url
        except subprocess.CalledProcessError:
            print("❌ Failed to push to GitHub. Check the URL and try again.")
            return None
    return None

def deploy_railway():
    """Deploy to Railway.app"""
    print("\n🚂 Deploying to Railway.app...")
    print("1. Go to https://railway.app")
    print("2. Sign up with GitHub")
    print("3. Click 'New Project'")
    print("4. Select 'Deploy from GitHub repo'")
    print("5. Choose your skillswap repository")
    print("6. Railway will auto-deploy!")
    
    webbrowser.open("https://railway.app")
    
    print("\n🔧 After deployment, add these environment variables in Railway dashboard:")
    print("OPENAI_API_KEY = your-openai-key")
    print("EMAIL = <EMAIL>")
    print("EMAIL_PASSWORD = your-app-password")
    print("RAZORPAY_KEY_ID = your-razorpay-key")
    print("RAZORPAY_KEY_SECRET = your-razorpay-secret")

def deploy_render():
    """Deploy to Render.com"""
    print("\n🎨 Deploying to Render.com...")
    print("1. Go to https://render.com")
    print("2. Sign up with GitHub")
    print("3. Click 'New Web Service'")
    print("4. Connect your GitHub repository")
    print("5. Render will auto-deploy!")
    
    webbrowser.open("https://render.com")
    
    print("\n🔧 After deployment, add environment variables in Render dashboard:")
    print("Same variables as Railway above")

def deploy_fly():
    """Deploy to Fly.io"""
    print("\n🪰 Deploying to Fly.io...")
    
    # Check if flyctl is installed
    try:
        subprocess.run(['fly', 'version'], capture_output=True, check=True)
        print("✅ Fly CLI found")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("📥 Installing Fly CLI...")
        if os.name == 'nt':  # Windows
            print("Run: iwr https://fly.io/install.ps1 -useb | iex")
        else:  # Unix/Linux/Mac
            subprocess.run(['curl', '-L', 'https://fly.io/install.sh', '|', 'sh'], shell=True)
    
    print("\n🚀 Deploy commands:")
    print("fly auth login")
    print("fly launch")
    print("fly deploy")

def create_deployment_files():
    """Create necessary deployment files"""
    files_created = []
    
    # Check if files exist
    required_files = [
        'railway.toml',
        'render.yaml', 
        'fly.toml',
        'Dockerfile',
        'requirements.txt'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            files_created.append(file)
    
    print(f"✅ Deployment files ready: {', '.join(files_created)}")
    return len(files_created) >= 3

def optimize_for_free_hosting():
    """Optimize app for free hosting limits"""
    print("\n⚡ Optimizing for free hosting...")
    
    # Create optimized requirements.txt
    optimized_requirements = """streamlit>=1.28.0
gspread>=5.10.0
oauth2client>=4.1.3
pandas>=2.0.0
requests>=2.31.0
openai>=1.0.0
razorpay>=1.3.0"""
    
    with open('requirements.txt', 'w') as f:
        f.write(optimized_requirements)
    
    print("✅ Requirements optimized for free hosting")
    
    # Create .gitignore if it doesn't exist
    gitignore_content = """.streamlit/secrets.toml
__pycache__/
*.pyc
.env
.venv/
venv/
.DS_Store
*.log"""
    
    if not os.path.exists('.gitignore'):
        with open('.gitignore', 'w') as f:
            f.write(gitignore_content)
        print("✅ .gitignore created")

def main():
    """Main deployment function"""
    print("🆓 SkillSwap FREE Deployment Helper")
    print("=" * 50)
    
    # Check prerequisites
    if not check_git():
        return
    
    # Optimize for free hosting
    optimize_for_free_hosting()
    
    # Create deployment files
    if not create_deployment_files():
        print("❌ Missing deployment files. Please run this from your SkillSwap directory.")
        return
    
    # Choose deployment platform
    print("\n🚀 Choose your FREE hosting platform:")
    print("1. Railway.app (Recommended - Easiest)")
    print("2. Render.com (750 hours/month)")
    print("3. Fly.io (Good performance)")
    print("4. Deploy to all platforms")
    
    choice = input("\nEnter your choice (1-4): ").strip()
    
    # Setup GitHub first
    print("\n📁 Setting up GitHub repository...")
    repo_url = create_github_repo()
    
    if not repo_url:
        print("❌ GitHub setup required for deployment")
        return
    
    # Deploy based on choice
    if choice == "1":
        deploy_railway()
    elif choice == "2":
        deploy_render()
    elif choice == "3":
        deploy_fly()
    elif choice == "4":
        print("\n🚀 Deploying to all platforms...")
        deploy_railway()
        deploy_render()
        deploy_fly()
    else:
        print("❌ Invalid choice")
        return
    
    # Final instructions
    print("\n🎉 Deployment initiated!")
    print("\n📋 Next Steps:")
    print("1. ✅ Complete the deployment on your chosen platform")
    print("2. 🔧 Add your API keys as environment variables")
    print("3. 🌐 Configure custom domain (optional)")
    print("4. 🧪 Test your live application")
    print("5. 📢 Share with users and start generating revenue!")
    
    print("\n💰 Revenue Potential:")
    print("- Week 1: 10-20 users → ₹100-500")
    print("- Month 1: 100+ users → ₹2,000+")
    print("- Month 3: 500+ users → ₹10,000+")
    
    print("\n🎯 Your SkillSwap platform will be live in 10-15 minutes!")
    print("📞 Need help? Check the FREE_HOSTING_GUIDE.md file")

if __name__ == "__main__":
    main()
