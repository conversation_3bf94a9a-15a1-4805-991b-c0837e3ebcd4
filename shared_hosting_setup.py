#!/usr/bin/env python3
"""
SkillSwap Shared Hosting Setup
For cPanel/shared hosting with Python support
"""

import os
import sys
import subprocess
import shutil

def check_python_version():
    """Check if Python 3.8+ is available"""
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python 3.8+ required. Current version:", sys.version)
        return False
    print(f"✅ Python {version.major}.{version.minor} detected")
    return True

def setup_virtual_environment():
    """Create virtual environment"""
    print("🔧 Setting up virtual environment...")
    
    if os.path.exists('venv'):
        shutil.rmtree('venv')
    
    subprocess.run([sys.executable, '-m', 'venv', 'venv'])
    
    # Activate virtual environment
    if os.name == 'nt':  # Windows
        pip_path = 'venv\\Scripts\\pip'
        python_path = 'venv\\Scripts\\python'
    else:  # Unix/Linux
        pip_path = 'venv/bin/pip'
        python_path = 'venv/bin/python'
    
    # Upgrade pip
    subprocess.run([python_path, '-m', 'pip', 'install', '--upgrade', 'pip'])
    
    return pip_path, python_path

def install_dependencies(pip_path):
    """Install required packages"""
    print("📦 Installing dependencies...")
    
    # Core dependencies for shared hosting
    packages = [
        'streamlit>=1.28.0',
        'gspread>=5.10.0',
        'oauth2client>=4.1.3',
        'pandas>=2.0.0',
        'requests>=2.31.0'
    ]
    
    # Optional dependencies
    optional_packages = [
        'openai>=1.0.0',
        'razorpay>=1.3.0',
        'email-validator>=2.0.0'
    ]
    
    # Install core packages
    for package in packages:
        try:
            subprocess.run([pip_path, 'install', package], check=True)
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"❌ Failed to install {package}")
    
    # Install optional packages
    for package in optional_packages:
        try:
            subprocess.run([pip_path, 'install', package], check=True)
            print(f"✅ Installed {package}")
        except subprocess.CalledProcessError:
            print(f"⚠️ Optional package {package} failed to install")

def create_wsgi_app():
    """Create WSGI application for shared hosting"""
    wsgi_content = '''
import sys
import os

# Add your project directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

# Activate virtual environment
activate_this = os.path.join(os.path.dirname(__file__), 'venv', 'bin', 'activate_this.py')
if os.path.exists(activate_this):
    exec(open(activate_this).read(), {'__file__': activate_this})

# Import your Streamlit app
from skillswap_simple import main

# WSGI application
def application(environ, start_response):
    """WSGI application entry point"""
    try:
        # Set environment variables
        os.environ.update(environ)
        
        # Run Streamlit app
        main()
        
        status = '200 OK'
        headers = [('Content-type', 'text/html')]
        start_response(status, headers)
        
        return [b'SkillSwap is running!']
    except Exception as e:
        status = '500 Internal Server Error'
        headers = [('Content-type', 'text/plain')]
        start_response(status, headers)
        return [f'Error: {str(e)}'.encode()]

# For testing
if __name__ == '__main__':
    from wsgiref.simple_server import make_server
    server = make_server('localhost', 8000, application)
    print("Serving on http://localhost:8000")
    server.serve_forever()
'''
    
    with open('app.wsgi', 'w') as f:
        f.write(wsgi_content)
    
    print("✅ Created WSGI application")

def create_htaccess():
    """Create .htaccess for Apache"""
    htaccess_content = '''
# SkillSwap .htaccess configuration

# Enable WSGI
AddHandler wsgi-script .wsgi
Options +ExecCGI

# Set default file
DirectoryIndex app.wsgi

# Security headers
Header always set X-Frame-Options "SAMEORIGIN"
Header always set X-XSS-Protection "1; mode=block"
Header always set X-Content-Type-Options "nosniff"

# Enable compression
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
</IfModule>

# Redirect HTTP to HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
'''
    
    with open('.htaccess', 'w') as f:
        f.write(htaccess_content)
    
    print("✅ Created .htaccess file")

def create_config_files():
    """Create configuration files"""
    
    # Create streamlit config
    os.makedirs('.streamlit', exist_ok=True)
    
    config_content = '''
[server]
port = 8501
address = "0.0.0.0"
headless = true
enableCORS = false
enableXsrfProtection = false

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#ff4b4b"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
'''
    
    with open('.streamlit/config.toml', 'w') as f:
        f.write(config_content)
    
    # Create secrets template
    secrets_template = '''
# Add your API keys here
OPENAI_API_KEY = "your-openai-api-key"
EMAIL = "<EMAIL>"
EMAIL_PASSWORD = "your-app-password"
RAZORPAY_KEY_ID = "your-razorpay-key-id"
RAZORPAY_KEY_SECRET = "your-razorpay-secret"

[gcp_service_account]
type = "service_account"
project_id = "your-project-id"
# ... add your service account details
'''
    
    with open('.streamlit/secrets.toml.template', 'w') as f:
        f.write(secrets_template)
    
    print("✅ Created configuration files")

def create_startup_script():
    """Create startup script for shared hosting"""
    startup_content = '''#!/bin/bash

# SkillSwap Startup Script for Shared Hosting

echo "🚀 Starting SkillSwap..."

# Activate virtual environment
source venv/bin/activate

# Set environment variables
export STREAMLIT_SERVER_PORT=8501
export STREAMLIT_SERVER_ADDRESS=0.0.0.0
export STREAMLIT_SERVER_HEADLESS=true

# Start Streamlit
streamlit run skillswap_simple.py --server.port=8501 --server.address=0.0.0.0 --server.headless=true

echo "✅ SkillSwap started successfully!"
'''
    
    with open('start.sh', 'w') as f:
        f.write(startup_content)
    
    os.chmod('start.sh', 0o755)
    print("✅ Created startup script")

def main():
    """Main setup function"""
    print("🏠 SkillSwap Shared Hosting Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Setup virtual environment
    pip_path, python_path = setup_virtual_environment()
    
    # Install dependencies
    install_dependencies(pip_path)
    
    # Create WSGI app
    create_wsgi_app()
    
    # Create .htaccess
    create_htaccess()
    
    # Create config files
    create_config_files()
    
    # Create startup script
    create_startup_script()
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Copy your SkillSwap files to this directory")
    print("2. Edit .streamlit/secrets.toml with your API keys")
    print("3. Upload everything to your shared hosting")
    print("4. Set your domain to point to the uploaded directory")
    print("5. Your app will be available at your domain!")
    
    print("\n🔧 For cPanel hosting:")
    print("- Upload files to public_html directory")
    print("- Ensure Python 3.8+ is enabled")
    print("- Set app.wsgi as the default document")
    
    print("\n📞 Need help? Check the documentation files!")

if __name__ == "__main__":
    main()
