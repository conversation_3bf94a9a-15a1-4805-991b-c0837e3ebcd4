# GitHub Actions Workflow for SkillSwap Free Deployment
name: Deploy SkillSwap

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Python 3.11
      uses: actions/setup-python@v3
      with:
        python-version: 3.11
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    
    - name: Test app syntax
      run: |
        python -m py_compile skillswap_simple.py
        echo "✅ App syntax is valid"
    
    - name: Check Streamlit config
      run: |
        if [ -f ".streamlit/config.toml" ]; then
          echo "✅ Streamlit config found"
        else
          echo "⚠️ No Streamlit config (optional)"
        fi

  deploy-info:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deployment Ready
      run: |
        echo "🚀 SkillSwap is ready for deployment!"
        echo "📋 Deploy to your chosen platform:"
        echo "1. Railway.app - Connect this GitHub repo"
        echo "2. Render.com - Connect this GitHub repo"  
        echo "3. Fly.io - Use fly deploy command"
        echo "4. Koyeb - Connect this GitHub repo"
        echo ""
        echo "🔧 Don't forget to add environment variables:"
        echo "- OPENAI_API_KEY"
        echo "- EMAIL" 
        echo "- EMAIL_PASSWORD"
        echo "- RAZORPAY_KEY_ID"
        echo "- RAZORPAY_KEY_SECRET"
        echo "- GCP_SERVICE_ACCOUNT (as JSON)"
        echo ""
        echo "🎉 Your revenue-generating platform will be live soon!"
