# SkillSwap Setup Guide

## 🚀 Quick Setup (7-10 days to launch)

### 1. Google Sheets Setup

Create 4 Google Sheets with these exact names and headers:

#### Sheet 1: "SkillSwap_Users"
Headers: `Timestamp | Name | Email | Skill_Have | Skill_Want | Availability | Bio | Hash | Status`

#### Sheet 2: "SkillSwap_Premium" 
Headers: `Timestamp | Name | Email | Payment_ID | Status | Expiry_Date`

#### Sheet 3: "SkillSwap_Matches"
Headers: `Timestamp | User1_Email | User2_Email | Status | Contact_Made`

#### Sheet 4: "SkillSwap_Payments"
Headers: `Timestamp | Email | Payment_ID | Amount | Status | Method`

### 2. Google Cloud Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google Sheets API and Google Drive API
4. Create a Service Account:
   - Go to IAM & Admin > Service Accounts
   - Create new service account
   - Download JSON key file
5. Share your Google Sheets with the service account email (Editor access)

### 3. OpenAI Setup

1. Go to [OpenAI Platform](https://platform.openai.com/)
2. Create account and add billing information
3. Generate API key from API Keys section
4. Add credits (minimum $5 recommended)

### 4. Razorpay Setup

1. Sign up at [Razorpay](https://razorpay.com/)
2. Complete KYC verification
3. Get API keys from Settings > API Keys
4. Enable Payment Links feature
5. Set up webhooks (optional for advanced features)

### 5. Gmail Setup

1. Enable 2-Factor Authentication on Gmail
2. Generate App Password:
   - Google Account > Security > App Passwords
   - Generate password for "Mail"
3. Use this app password in secrets.toml

### 6. Streamlit Cloud Deployment

1. Push code to GitHub repository
2. Go to [Streamlit Cloud](https://share.streamlit.io/)
3. Connect GitHub and deploy
4. Add secrets in Streamlit Cloud dashboard
5. Set main file as `skillswap_app.py`

### 7. Configuration

1. Copy `secrets_template.toml` to `.streamlit/secrets.toml`
2. Fill in all your API keys and credentials
3. Test locally: `streamlit run skillswap_app.py`

## 💰 Revenue Projections

### Conservative Estimates:
- Month 1: 50 users, 10 premium (₹490)
- Month 3: 200 users, 50 premium (₹2,450)
- Month 6: 500 users, 150 premium (₹7,350)
- Month 12: 1000 users, 300 premium (₹14,700)

### Growth Strategies:
1. **Content Marketing**: Blog about skill exchange
2. **Social Media**: LinkedIn, Twitter skill communities
3. **Referral Program**: Discount for referrals
4. **Corporate Partnerships**: Company skill exchange programs
5. **Influencer Collaborations**: Partner with educators

## 🔧 Customization Options

### Pricing Changes:
- Modify `amount: 4900` in `create_payment_link()` function
- Update UI text showing ₹49/month

### Adding Features:
- Video call integration (Zoom API)
- Skill verification system
- Rating and review system
- Group learning sessions
- Skill certificates

### Scaling Considerations:
- Move from Google Sheets to proper database (PostgreSQL)
- Add caching with Redis
- Implement proper user authentication
- Add admin dashboard
- Set up monitoring and analytics

## 🚨 Important Notes

1. **Legal Compliance**: Add Terms of Service and Privacy Policy
2. **Data Protection**: Implement GDPR compliance if targeting EU users
3. **Payment Security**: Never store payment details locally
4. **Email Limits**: Gmail has sending limits, consider SendGrid for scale
5. **API Limits**: Monitor OpenAI and other API usage

## 📞 Support

For technical issues:
1. Check Streamlit logs in cloud dashboard
2. Verify all API keys are correct
3. Ensure Google Sheets are properly shared
4. Test payment flow in Razorpay test mode first

## 🎯 Launch Checklist

- [ ] All Google Sheets created with correct headers
- [ ] Service account has access to sheets
- [ ] OpenAI API key working with credits
- [ ] Razorpay account verified and keys obtained
- [ ] Gmail app password generated
- [ ] Streamlit app deployed and accessible
- [ ] Payment flow tested end-to-end
- [ ] Terms of Service and Privacy Policy added
- [ ] Basic marketing materials prepared

**Estimated Setup Time: 2-3 days**
**Estimated Launch Time: 7-10 days including testing**
