import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime, timedelta
import hashlib
import time

# Professional Design System
PRIMARY_COLOR = "#2563EB"      # Blue-600 - Professional, trustworthy
SECONDARY_COLOR = "#059669"    # Emerald-600 - Success, growth
ACCENT_COLOR = "#DC2626"       # Red-600 - Attention, urgency
BACKGROUND_COLOR = "#F8FAFC"   # Slate-50 - Clean background
TEXT_COLOR = "#1E293B"         # Slate-800 - Primary text
MUTED_COLOR = "#64748B"        # Slate-500 - Secondary text
BORDER_COLOR = "#E2E8F0"       # Slate-200 - Borders
SUCCESS_COLOR = "#059669"      # Emerald-600 - Success states

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Professional Skill Exchange Platform",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Professional CSS styling
st.markdown(f"""
<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    .main {{
        font-family: 'Inter', sans-serif;
        background: {BACKGROUND_COLOR};
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    
    /* Professional Header */
    .header-container {{
        background: linear-gradient(135deg, {PRIMARY_COLOR} 0%, {SECONDARY_COLOR} 100%);
        padding: 2rem;
        border-radius: 8px;
        margin-bottom: 2rem;
        color: white;
        text-align: center;
    }}
    
    .header-title {{
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
    }}
    
    .header-subtitle {{
        font-size: 1.2rem;
        opacity: 0.9;
        margin: 0;
    }}
    
    /* Professional Cards */
    .card {{
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
        border: 1px solid {BORDER_COLOR};
        margin-bottom: 1.5rem;
        transition: box-shadow 0.2s ease;
    }}
    
    .card:hover {{
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }}
    
    /* Statistics Grid */
    .stats-grid {{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin: 2rem 0;
    }}
    
    .stat-card {{
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        text-align: center;
        border: 1px solid {BORDER_COLOR};
    }}
    
    .stat-number {{
        font-size: 2.5rem;
        font-weight: 700;
        color: {PRIMARY_COLOR};
        display: block;
        margin-bottom: 0.5rem;
    }}
    
    .stat-label {{
        color: {MUTED_COLOR};
        font-size: 0.9rem;
    }}
    
    /* Professional Buttons */
    .btn-primary {{
        background: {PRIMARY_COLOR};
        color: white;
        padding: 0.75rem 1.5rem;
        border: none;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: background-color 0.2s ease;
        text-decoration: none;
        display: inline-block;
    }}
    
    .btn-primary:hover {{
        background: #1D4ED8;
    }}
    
    .btn-secondary {{
        background: white;
        color: {PRIMARY_COLOR};
        padding: 0.75rem 1.5rem;
        border: 2px solid {PRIMARY_COLOR};
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
    }}
    
    .btn-secondary:hover {{
        background: {PRIMARY_COLOR};
        color: white;
    }}
    
    /* Professional Badges */
    .badge {{
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 4px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
    }}
    
    .badge-success {{
        background: {SUCCESS_COLOR};
        color: white;
    }}
    
    .badge-info {{
        background: {PRIMARY_COLOR};
        color: white;
    }}
    
    /* Testimonial Cards */
    .testimonial {{
        background: white;
        padding: 1.5rem;
        border-radius: 8px;
        border-left: 4px solid {PRIMARY_COLOR};
        margin: 1rem 0;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }}
    
    .testimonial-text {{
        font-style: italic;
        color: {TEXT_COLOR};
        margin-bottom: 1rem;
        line-height: 1.6;
    }}
    
    .testimonial-author {{
        font-weight: 600;
        color: {PRIMARY_COLOR};
    }}
    
    .testimonial-role {{
        color: {MUTED_COLOR};
        font-size: 0.9rem;
    }}
    
    /* Form Styling */
    .stTextInput > div > div > input {{
        border-radius: 6px;
        border: 2px solid {BORDER_COLOR};
        padding: 0.75rem;
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }}
    
    .stTextInput > div > div > input:focus {{
        border-color: {PRIMARY_COLOR};
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }}
    
    .stSelectbox > div > div > select {{
        border-radius: 6px;
        border: 2px solid {BORDER_COLOR};
        padding: 0.75rem;
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .header-container {{
            padding: 1.5rem;
        }}
        
        .header-title {{
            font-size: 2rem;
        }}
        
        .card {{
            padding: 1.5rem;
        }}
        
        .stats-grid {{
            grid-template-columns: repeat(2, 1fr);
        }}
    }}
    
    /* Professional spacing */
    .section-title {{
        font-size: 1.5rem;
        font-weight: 600;
        color: {TEXT_COLOR};
        margin: 2rem 0 1rem 0;
    }}
    
    .section-subtitle {{
        color: {MUTED_COLOR};
        margin-bottom: 1.5rem;
    }}
</style>
""", unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "welcome"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False

# Professional trust signals
def show_trust_signals():
    st.markdown(f"""
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">2,847</span>
            <div class="stat-label">Active Members</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">1,293</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">89%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def show_testimonials():
    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months. The platform made it easy to find the right match.",
            "author": "Rahul Kumar",
            "role": "Software Developer"
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections. Best investment I've made for my career.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer"
        },
        {
            "text": "Worth every rupee. The quality of matches is outstanding and the platform is easy to use. Highly recommend for serious learners.",
            "author": "Arjun Singh",
            "role": "Data Analyst"
        }
    ]
    
    for testimonial in testimonials:
        st.markdown(f"""
        <div class="testimonial">
            <div class="testimonial-text">"{testimonial['text']}"</div>
            <div class="testimonial-author">{testimonial['author']}</div>
            <div class="testimonial-role">{testimonial['role']}</div>
        </div>
        """, unsafe_allow_html=True)

def show_popular_skills():
    skills = [
        "Web Development", "Digital Marketing", "Data Science", "UI/UX Design",
        "Content Writing", "Photography", "Python", "React", "SEO", "Graphic Design",
        "Video Editing", "Social Media", "Excel", "Public Speaking", "Language Learning"
    ]
    
    st.markdown('<h3 class="section-title">Popular Skills</h3>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Most requested skills in our community</p>', unsafe_allow_html=True)
    
    # Create skill tags without emojis
    skills_html = ""
    for skill in skills:
        skills_html += f'<span class="badge badge-info">{skill}</span>'
    
    st.markdown(f'<div style="margin: 1rem 0;">{skills_html}</div>', unsafe_allow_html=True)

# Main application
def main():
    init_session_state()
    
    # Professional header
    st.markdown(f"""
    <div class="header-container">
        <h1 class="header-title">SkillSwap</h1>
        <p class="header-subtitle">Professional Skill Exchange Platform</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Page routing
    if st.session_state.page == "welcome":
        show_welcome_page()
    elif st.session_state.page == "onboarding":
        show_onboarding_page()
    elif st.session_state.page == "payment":
        show_payment_page()
    elif st.session_state.page == "dashboard":
        show_dashboard_page()

def show_welcome_page():
    """Professional welcome page with clear value proposition"""

    # Hero section
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown(f"""
        <div class="card">
            <h2 style="color: {PRIMARY_COLOR}; font-size: 2.2rem; margin-bottom: 1rem;">
                Exchange Skills. Build Your Career. Grow Your Network.
            </h2>
            <p style="font-size: 1.1rem; color: {MUTED_COLOR}; margin-bottom: 2rem; line-height: 1.6;">
                Join 2,800+ professionals who are teaching what they know and learning what they need.
                Quality matches, verified members, guaranteed results.
            </p>

            <div style="margin-bottom: 2rem;">
                <span class="badge badge-success">Verified Community</span>
                <span class="badge badge-success">Perfect Matches</span>
                <span class="badge badge-success">Direct Contact</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("Start Your Learning Journey", key="start_journey", help="Join the community"):
            st.session_state.page = "onboarding"
            st.rerun()

    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <h3 style="color: {PRIMARY_COLOR}; margin-bottom: 1.5rem;">How It Works</h3>
            <div style="margin: 1.5rem 0;">
                <div style="margin: 1.5rem 0;">
                    <div style="background: {PRIMARY_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">1</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Share your expertise</p>
                </div>
                <div style="margin: 1.5rem 0;">
                    <div style="background: {SECONDARY_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">2</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Get matched with learners</p>
                </div>
                <div style="margin: 1.5rem 0;">
                    <div style="background: {ACCENT_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">3</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Exchange knowledge</p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Trust signals
    show_trust_signals()

    # Popular skills
    show_popular_skills()

    # Testimonials
    st.markdown('<h3 class="section-title">What Our Members Say</h3>', unsafe_allow_html=True)
    show_testimonials()

    # Pricing
    st.markdown(f"""
    <div class="card" style="text-align: center; background: linear-gradient(135deg, {PRIMARY_COLOR}10, {SECONDARY_COLOR}10);">
        <h3 style="color: {PRIMARY_COLOR}; margin-bottom: 1rem;">Simple, Transparent Pricing</h3>
        <div style="font-size: 3rem; font-weight: 700; color: {PRIMARY_COLOR}; margin: 1rem 0;">₹49</div>
        <p style="color: {MUTED_COLOR}; margin-bottom: 2rem;">per month • Cancel anytime</p>

        <div style="text-align: left; max-width: 300px; margin: 0 auto;">
            <p style="margin: 0.5rem 0;">✓ Unlimited skill matches</p>
            <p style="margin: 0.5rem 0;">✓ Direct contact information</p>
            <p style="margin: 0.5rem 0;">✓ Priority matching algorithm</p>
            <p style="margin: 0.5rem 0;">✓ Community support</p>
            <p style="margin: 0.5rem 0;">✓ Progress tracking</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

def show_onboarding_page():
    """Professional onboarding form"""

    st.markdown(f"""
    <div class="card">
        <h2 style="color: {PRIMARY_COLOR}; text-align: center; margin-bottom: 1rem;">
            Tell Us About Your Skills
        </h2>
        <p style="text-align: center; color: {MUTED_COLOR}; margin-bottom: 2rem;">
            Help us find your perfect learning partners. The more specific you are, the better your matches.
        </p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("skill_form", clear_on_submit=False):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown('<h4 style="color: #1E293B; margin-bottom: 1rem;">Personal Information</h4>', unsafe_allow_html=True)
            name = st.text_input(
                "Full Name *",
                placeholder="e.g., Priya Sharma",
                help="This will be visible to your matches"
            )
            email = st.text_input(
                "Email Address *",
                placeholder="<EMAIL>",
                help="We'll send you match notifications here"
            )

            st.markdown('<h4 style="color: #1E293B; margin: 2rem 0 1rem 0;">What You Can Teach</h4>', unsafe_allow_html=True)
            skill_have = st.text_input(
                "Your Expertise *",
                placeholder="e.g., React Development, Digital Marketing",
                help="Be specific for better matches"
            )

            skill_level_teach = st.selectbox(
                "Your Experience Level",
                ["Beginner (1-2 years)", "Intermediate (2-5 years)", "Advanced (5+ years)", "Expert (Professional)"],
                index=1
            )

        with col2:
            st.markdown('<h4 style="color: #1E293B; margin-bottom: 1rem;">What You Want to Learn</h4>', unsafe_allow_html=True)
            skill_want = st.text_input(
                "Skill You Want to Learn *",
                placeholder="e.g., UI/UX Design, Content Writing",
                help="What skill would help you grow professionally?"
            )

            skill_level_learn = st.selectbox(
                "Your Current Level",
                ["Complete Beginner", "Some Knowledge", "Intermediate", "Need Advanced Help"],
                index=0
            )

            st.markdown('<h4 style="color: #1E293B; margin: 2rem 0 1rem 0;">Availability</h4>', unsafe_allow_html=True)
            availability = st.selectbox(
                "When Can You Meet? *",
                [
                    "Weekends Only",
                    "Weekday Evenings (6-9 PM)",
                    "Flexible Schedule",
                    "Weekday Mornings",
                    "Custom Schedule"
                ]
            )

            if availability == "Custom Schedule":
                custom_availability = st.text_input(
                    "Specify Your Availability",
                    placeholder="e.g., Tuesdays and Thursdays 7-9 PM"
                )

        # Learning goals
        st.markdown('<h4 style="color: #1E293B; margin: 2rem 0 1rem 0;">Learning Goals (Optional)</h4>', unsafe_allow_html=True)
        learning_goal = st.text_area(
            "What do you hope to achieve?",
            placeholder="e.g., I want to transition to a UX role or start freelancing",
            height=100
        )

        # Commitment level
        st.markdown('<h4 style="color: #1E293B; margin: 2rem 0 1rem 0;">Time Commitment</h4>', unsafe_allow_html=True)
        commitment = st.radio(
            "How much time can you dedicate weekly?",
            ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
            horizontal=True
        )

        # Terms and submit
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            terms_agreed = st.checkbox(
                "I agree to the Terms of Service and Community Guidelines",
                help="By joining, you commit to respectful learning and teaching"
            )

            submitted = st.form_submit_button(
                "Join the Community",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([name, email, skill_have, skill_want, availability, terms_agreed]):
                st.error("Please fill in all required fields and agree to the terms.")
            elif "@" not in email:
                st.error("Please enter a valid email address.")
            else:
                # Store user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability

                st.session_state.user_data = {
                    'name': name,
                    'email': email,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'skill_level_teach': skill_level_teach,
                    'skill_level_learn': skill_level_learn,
                    'availability': final_availability,
                    'learning_goal': learning_goal,
                    'commitment': commitment,
                    'bio': f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. {learning_goal}"
                }

                st.session_state.page = "payment"
                st.rerun()

def show_payment_page():
    """Professional payment page with clear value"""

    user_data = st.session_state.user_data

    st.markdown(f"""
    <div class="card" style="text-align: center;">
        <h2 style="color: {PRIMARY_COLOR}; margin-bottom: 1rem;">
            Welcome to SkillSwap, {user_data.get('name', 'Friend')}!
        </h2>
        <p style="color: {MUTED_COLOR}; font-size: 1.1rem; margin-bottom: 2rem;">
            You're one step away from connecting with amazing learning partners.
        </p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {PRIMARY_COLOR}; margin-bottom: 1rem;">Your Learning Profile</h3>
            <div style="margin: 1rem 0;">
                <p><strong>Can Teach:</strong> {user_data.get('skill_have', 'N/A')}</p>
                <p><strong>Wants to Learn:</strong> {user_data.get('skill_want', 'N/A')}</p>
                <p><strong>Availability:</strong> {user_data.get('availability', 'N/A')}</p>
                <p><strong>Commitment:</strong> {user_data.get('commitment', 'N/A')} per week</p>
            </div>

            <div style="background: {SUCCESS_COLOR}10; padding: 1rem; border-radius: 6px; border-left: 4px solid {SUCCESS_COLOR};">
                <strong style="color: {SUCCESS_COLOR};">Potential Matches Found!</strong><br>
                <span style="color: {MUTED_COLOR};">We found 12 people who want to learn {user_data.get('skill_have', 'your skill')}
                and can teach {user_data.get('skill_want', 'what you want to learn')}.</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {PRIMARY_COLOR}; text-align: center; margin-bottom: 1rem;">Unlock Your Matches</h3>

            <div style="text-align: center; margin: 2rem 0;">
                <div style="font-size: 3rem; font-weight: 700; color: {PRIMARY_COLOR};">₹49</div>
                <p style="color: {MUTED_COLOR};">per month • Cancel anytime</p>
            </div>

            <div style="text-align: left;">
                <p style="margin: 0.75rem 0;">✓ Access to all 12 potential matches</p>
                <p style="margin: 0.75rem 0;">✓ Direct contact information</p>
                <p style="margin: 0.75rem 0;">✓ Conversation starters</p>
                <p style="margin: 0.75rem 0;">✓ Priority in future matches</p>
                <p style="margin: 0.75rem 0;">✓ Community support</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Payment form
    st.markdown(f"""
    <div class="card">
        <h3 style="color: {PRIMARY_COLOR}; text-align: center; margin-bottom: 1rem;">Complete Your Membership</h3>
    </div>
    """, unsafe_allow_html=True)

    with st.form("payment_form"):
        col1, col2 = st.columns(2)

        with col1:
            payment_name = st.text_input(
                "Full Name for Payment",
                value=user_data.get('name', ''),
                help="As it appears on your payment method"
            )
            payment_email = st.text_input(
                "Email for Receipt",
                value=user_data.get('email', ''),
                help="We'll send your receipt here"
            )

        with col2:
            st.markdown('<h4 style="color: #1E293B;">Secure Payment</h4>', unsafe_allow_html=True)
            st.info("We use Razorpay for secure payments. Your data is encrypted and safe. Cancel anytime, no hidden fees.")

        # Trust signals
        st.markdown(f"""
        <div style="text-align: center; margin: 1.5rem 0;">
            <span class="badge badge-success">256-bit SSL Encryption</span>
            <span class="badge badge-success">Razorpay Secured</span>
            <span class="badge badge-success">Money-back Guarantee</span>
        </div>
        """, unsafe_allow_html=True)

        terms_payment = st.checkbox(
            "I agree to the payment terms and monthly subscription",
            help="You can cancel anytime from your dashboard"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button(
                "Start Learning - ₹49/month",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([payment_name, payment_email, terms_payment]):
                st.error("Please fill all fields and agree to terms")
            else:
                st.success("Payment successful! Welcome to SkillSwap Premium!")
                st.session_state.authenticated = True
                st.session_state.page = "dashboard"
                time.sleep(2)
                st.rerun()

def show_dashboard_page():
    """Professional dashboard"""

    user_data = st.session_state.user_data

    # Welcome header
    st.markdown(f"""
    <div class="card" style="background: linear-gradient(135deg, {PRIMARY_COLOR}, {SECONDARY_COLOR}); color: white;">
        <h2 style="margin-bottom: 0.5rem;">Welcome back, {user_data.get('name', 'Friend')}!</h2>
        <p style="opacity: 0.9; margin: 0;">Ready to connect with your learning partners?</p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="font-size: 2rem; color: {PRIMARY_COLOR}; font-weight: 700;">12</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Available Matches</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="font-size: 2rem; color: {SECONDARY_COLOR}; font-weight: 700;">3</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Conversations</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="font-size: 2rem; color: {ACCENT_COLOR}; font-weight: 700;">1</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Active Exchange</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="font-size: 2rem; color: {SUCCESS_COLOR}; font-weight: 700;">95%</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Match Quality</div>
        </div>
        """, unsafe_allow_html=True)

    # Main content tabs
    tab1, tab2, tab3 = st.tabs(["Your Matches", "Conversations", "Progress"])

    with tab1:
        show_matches_section(user_data)

    with tab2:
        st.markdown('<h3 class="section-title">Your Conversations</h3>', unsafe_allow_html=True)
        st.info("Feature coming soon! You'll be able to chat directly with your matches here.")

    with tab3:
        st.markdown('<h3 class="section-title">Your Learning Journey</h3>', unsafe_allow_html=True)
        st.info("Track your skill exchanges, learning goals, and achievements here.")

def show_matches_section(user_data):
    """Show professional match profiles"""

    # Sample matches data
    matches = [
        {
            "name": "Arjun Patel",
            "skill_have": "UI/UX Design",
            "skill_want": "React Development",
            "experience": "3 years",
            "availability": "Weekday Evenings",
            "bio": "Product designer at a fintech startup. Love creating user-centered designs and want to learn frontend development to better collaborate with developers.",
            "match_score": 95,
            "verified": True,
            "rating": 4.9,
            "exchanges": 5
        },
        {
            "name": "Priya Sharma",
            "skill_have": "Content Writing",
            "skill_want": "React Development",
            "experience": "4 years",
            "availability": "Flexible Schedule",
            "bio": "Freelance content writer specializing in tech and SaaS. Want to transition into tech by learning React and building my own projects.",
            "match_score": 88,
            "verified": True,
            "rating": 4.8,
            "exchanges": 3
        }
    ]

    st.markdown('<h3 class="section-title">Perfect Matches for You</h3>', unsafe_allow_html=True)
    st.markdown(f'<p class="section-subtitle">People who want to learn <strong>{user_data.get("skill_have", "your skill")}</strong> and can teach <strong>{user_data.get("skill_want", "what you want")}</strong></p>', unsafe_allow_html=True)

    for match in matches:
        st.markdown(f"""
        <div class="card">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                <div>
                    <h3 style="color: {PRIMARY_COLOR}; margin: 0; display: flex; align-items: center;">
                        {match['name']}
                        {'<span style="color: ' + SUCCESS_COLOR + '; margin-left: 0.5rem;">✓ Verified</span>' if match['verified'] else ''}
                    </h3>
                    <div style="color: {MUTED_COLOR}; font-size: 0.9rem; margin-top: 0.25rem;">
                        {match['rating']} rating • {match['exchanges']} successful exchanges
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="background: {SUCCESS_COLOR}; color: white; padding: 0.25rem 0.75rem;
                         border-radius: 4px; font-size: 0.875rem; font-weight: 500;">
                        {match['match_score']}% Match
                    </div>
                </div>
            </div>

            <div style="margin: 1rem 0;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                    <div>
                        <strong style="color: {SECONDARY_COLOR};">Can Teach:</strong><br>
                        <span style="color: {TEXT_COLOR};">{match['skill_have']} ({match['experience']})</span>
                    </div>
                    <div>
                        <strong style="color: {PRIMARY_COLOR};">Wants to Learn:</strong><br>
                        <span style="color: {TEXT_COLOR};">{match['skill_want']}</span>
                    </div>
                </div>

                <div style="margin: 1rem 0;">
                    <strong>About:</strong><br>
                    <span style="color: {TEXT_COLOR};">{match['bio']}</span>
                </div>

                <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">
                    Available: {match['availability']}
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-top: 1.5rem;">
                <button style="background: {PRIMARY_COLOR}; color: white; border: none;
                       padding: 0.75rem; border-radius: 6px; font-weight: 500; cursor: pointer;">
                    Start Conversation
                </button>
                <button style="background: white; color: {PRIMARY_COLOR}; border: 2px solid {PRIMARY_COLOR};
                       padding: 0.75rem; border-radius: 6px; font-weight: 500; cursor: pointer;">
                    View Full Profile
                </button>
            </div>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
