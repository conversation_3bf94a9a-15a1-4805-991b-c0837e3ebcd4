"""
SkillSwap UI Components Library
Reusable components for premium feel and mobile optimization
"""

import streamlit as st

# Design tokens
DESIGN_TOKENS = {
    "colors": {
        "primary": "#6366F1",
        "secondary": "#10B981", 
        "accent": "#F59E0B",
        "background": "#F8FAFC",
        "text": "#1E293B",
        "muted": "#64748B",
        "success": "#059669",
        "warning": "#D97706",
        "error": "#DC2626"
    },
    "spacing": {
        "xs": "0.25rem",
        "sm": "0.5rem", 
        "md": "1rem",
        "lg": "1.5rem",
        "xl": "2rem",
        "2xl": "3rem"
    },
    "typography": {
        "font_family": "'Inter', sans-serif",
        "font_sizes": {
            "xs": "0.75rem",
            "sm": "0.875rem",
            "base": "1rem",
            "lg": "1.125rem",
            "xl": "1.25rem",
            "2xl": "1.5rem",
            "3xl": "2rem"
        }
    },
    "shadows": {
        "sm": "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
        "md": "0 4px 6px -1px rgba(0, 0, 0, 0.1)",
        "lg": "0 10px 15px -3px rgba(0, 0, 0, 0.1)",
        "xl": "0 20px 25px -5px rgba(0, 0, 0, 0.1)"
    }
}

def load_custom_css():
    """Load custom CSS for premium UI components"""
    st.markdown("""
    <style>
    /* Mobile-first responsive design */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    .skillswap-container {
        font-family: 'Inter', sans-serif;
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 1rem;
    }
    
    /* Premium Card Component */
    .premium-card {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid #E2E8F0;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }
    
    .premium-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
    }
    
    /* Button Components */
    .btn-primary {
        background: linear-gradient(135deg, #6366F1, #10B981);
        color: white;
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-primary:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
    }
    
    .btn-secondary {
        background: white;
        color: #6366F1;
        border: 2px solid #6366F1;
        padding: 0.75rem 1.5rem;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
    }
    
    .btn-secondary:hover {
        background: #6366F1;
        color: white;
    }
    
    /* Badge Components */
    .badge {
        display: inline-flex;
        align-items: center;
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
    }
    
    .badge-success {
        background: #059669;
        color: white;
    }
    
    .badge-warning {
        background: #D97706;
        color: white;
    }
    
    .badge-info {
        background: #0EA5E9;
        color: white;
    }
    
    /* Avatar Component */
    .avatar {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        background: linear-gradient(135deg, #6366F1, #10B981);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
    }
    
    .avatar-lg {
        width: 64px;
        height: 64px;
        font-size: 1.5rem;
    }
    
    /* Progress Bar */
    .progress-bar {
        width: 100%;
        height: 8px;
        background: #E2E8F0;
        border-radius: 4px;
        overflow: hidden;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #6366F1, #10B981);
        transition: width 0.3s ease;
    }
    
    /* Mobile Optimizations */
    @media (max-width: 768px) {
        .skillswap-container {
            padding: 0 0.5rem;
        }
        
        .premium-card {
            padding: 1rem;
            border-radius: 12px;
        }
        
        .btn-primary, .btn-secondary {
            width: 100%;
            margin-bottom: 0.5rem;
        }
        
        .mobile-stack {
            flex-direction: column !important;
        }
        
        .mobile-full-width {
            width: 100% !important;
        }
    }
    
    /* Accessibility */
    .btn-primary:focus, .btn-secondary:focus {
        outline: 2px solid #6366F1;
        outline-offset: 2px;
    }
    
    /* Loading States */
    .loading-skeleton {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
    }
    
    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }
    </style>
    """, unsafe_allow_html=True)

def create_avatar(name, size="md"):
    """Create user avatar with initials"""
    initials = "".join([word[0].upper() for word in name.split()[:2]])
    size_class = "avatar-lg" if size == "lg" else "avatar"
    
    return f'<div class="{size_class}">{initials}</div>'

def create_badge(text, type="info"):
    """Create status badge"""
    return f'<span class="badge badge-{type}">{text}</span>'

def create_progress_bar(percentage):
    """Create progress bar"""
    return f'''
    <div class="progress-bar">
        <div class="progress-fill" style="width: {percentage}%"></div>
    </div>
    '''

def create_skill_card(skill_name, level, description, match_count=0):
    """Create skill showcase card"""
    colors = DESIGN_TOKENS["colors"]
    
    return f'''
    <div class="premium-card">
        <div style="display: flex; justify-content: space-between; align-items: start; margin-bottom: 1rem;">
            <div>
                <h3 style="color: {colors['primary']}; margin: 0;">{skill_name}</h3>
                <p style="color: {colors['muted']}; margin: 0.25rem 0;">{level}</p>
            </div>
            {create_badge(f"{match_count} matches", "success") if match_count > 0 else ""}
        </div>
        <p style="color: {colors['text']}; margin-bottom: 1rem;">{description}</p>
        <button class="btn-secondary" style="width: 100%;">View Matches</button>
    </div>
    '''

def create_match_card(match_data):
    """Create match profile card"""
    colors = DESIGN_TOKENS["colors"]
    
    return f'''
    <div class="premium-card">
        <div style="display: flex; align-items: center; margin-bottom: 1rem;">
            {create_avatar(match_data['name'])}
            <div style="margin-left: 1rem; flex: 1;">
                <h3 style="color: {colors['primary']}; margin: 0; display: flex; align-items: center;">
                    {match_data['name']}
                    {create_badge("✓ Verified", "success") if match_data.get('verified') else ""}
                </h3>
                <p style="color: {colors['muted']}; margin: 0.25rem 0;">
                    ⭐ {match_data.get('rating', 'New')} • {match_data.get('exchanges', 0)} exchanges
                </p>
            </div>
            <div class="badge badge-success">{match_data.get('match_score', 0)}% Match</div>
        </div>
        
        <div style="margin: 1rem 0;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1rem;">
                <div>
                    <strong style="color: {colors['secondary']};">Can Teach:</strong><br>
                    <span>{match_data.get('skill_have', 'N/A')}</span>
                </div>
                <div>
                    <strong style="color: {colors['primary']};">Wants to Learn:</strong><br>
                    <span>{match_data.get('skill_want', 'N/A')}</span>
                </div>
            </div>
            
            <p style="color: {colors['text']};">{match_data.get('bio', 'No bio available.')}</p>
            
            <p style="color: {colors['muted']}; font-size: 0.9rem;">
                📅 {match_data.get('availability', 'Flexible')}
            </p>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem;">
            <button class="btn-primary">💬 Connect</button>
            <button class="btn-secondary">👀 Profile</button>
        </div>
    </div>
    '''

def create_stats_grid(stats):
    """Create statistics grid"""
    colors = DESIGN_TOKENS["colors"]
    
    stats_html = '<div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 1rem; margin: 1rem 0;">'
    
    for stat in stats:
        stats_html += f'''
        <div class="premium-card" style="text-align: center; padding: 1rem;">
            <div style="font-size: 2rem; font-weight: 700; color: {colors['primary']}; margin-bottom: 0.5rem;">
                {stat['value']}
            </div>
            <div style="color: {colors['muted']}; font-size: 0.9rem;">
                {stat['label']}
            </div>
        </div>
        '''
    
    stats_html += '</div>'
    return stats_html

def create_testimonial_card(testimonial):
    """Create testimonial card"""
    colors = DESIGN_TOKENS["colors"]
    
    return f'''
    <div class="premium-card" style="border-left: 4px solid {colors['accent']};">
        <p style="font-style: italic; color: {colors['text']}; margin-bottom: 1rem;">
            "{testimonial['text']}"
        </p>
        <div style="display: flex; align-items: center;">
            {create_avatar(testimonial['author'])}
            <div style="margin-left: 1rem;">
                <div style="font-weight: 600; color: {colors['primary']};">
                    {testimonial['author']}
                </div>
                <div style="color: {colors['muted']}; font-size: 0.9rem;">
                    {testimonial.get('title', 'SkillSwap Member')}
                </div>
            </div>
        </div>
    </div>
    '''

def create_mobile_navigation():
    """Create mobile-friendly navigation"""
    return '''
    <div style="position: fixed; bottom: 0; left: 0; right: 0; background: white; 
         border-top: 1px solid #E2E8F0; padding: 0.5rem; z-index: 1000;
         display: flex; justify-content: space-around;">
        <button style="background: none; border: none; padding: 0.5rem; color: #6366F1;">
            🏠<br><span style="font-size: 0.75rem;">Home</span>
        </button>
        <button style="background: none; border: none; padding: 0.5rem; color: #64748B;">
            🎯<br><span style="font-size: 0.75rem;">Matches</span>
        </button>
        <button style="background: none; border: none; padding: 0.5rem; color: #64748B;">
            💬<br><span style="font-size: 0.75rem;">Chat</span>
        </button>
        <button style="background: none; border: none; padding: 0.5rem; color: #64748B;">
            👤<br><span style="font-size: 0.75rem;">Profile</span>
        </button>
    </div>
    '''

def show_loading_skeleton():
    """Show loading skeleton for better UX"""
    return '''
    <div class="premium-card">
        <div class="loading-skeleton" style="height: 20px; border-radius: 4px; margin-bottom: 1rem;"></div>
        <div class="loading-skeleton" style="height: 16px; border-radius: 4px; width: 80%; margin-bottom: 0.5rem;"></div>
        <div class="loading-skeleton" style="height: 16px; border-radius: 4px; width: 60%;"></div>
    </div>
    '''
