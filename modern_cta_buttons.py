import streamlit as st

# Page configuration
st.set_page_config(
    page_title="Modern CTA Buttons - SkillSwap",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="collapsed"
)

def load_modern_button_styles():
    """Load modern CTA button styles with professional SaaS aesthetic"""
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
        background: #F8FAFC;
        padding: 2rem 0;
    }
    
    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {visibility: hidden;}
    
    /* Container */
    .button-showcase {
        max-width: 800px;
        margin: 0 auto;
        padding: 3rem 2rem;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    }
    
    /* Button Container */
    .cta-button-container {
        display: flex;
        gap: 1.5rem;
        justify-content: center;
        align-items: center;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    /* PRIMARY BUTTON - Vibrant Gradient */
    .btn-primary-modern {
        background: linear-gradient(135deg, #2563EB 0%, #0891B2 100%);
        color: white;
        padding: 16px 32px;
        border: none;
        border-radius: 10px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        min-width: 180px;
        box-shadow: 0 4px 14px rgba(37, 99, 235, 0.3);
        position: relative;
        overflow: hidden;
    }
    
    .btn-primary-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #1D4ED8 0%, #0E7490 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .btn-primary-modern:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4);
    }
    
    .btn-primary-modern:hover::before {
        opacity: 1;
    }
    
    .btn-primary-modern span {
        position: relative;
        z-index: 1;
    }
    
    .btn-primary-modern .icon {
        position: relative;
        z-index: 1;
        font-size: 18px;
    }
    
    /* SECONDARY BUTTON - Minimalist */
    .btn-secondary-modern {
        background: white;
        color: #374151;
        padding: 16px 32px;
        border: 2px solid #D1D5DB;
        border-radius: 10px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        min-width: 140px;
        position: relative;
    }
    
    .btn-secondary-modern:hover {
        background: #F3F4F6;
        border-color: #9CA3AF;
        color: #1F2937;
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .btn-secondary-modern .icon {
        font-size: 16px;
        opacity: 0.8;
    }
    
    /* Alternative Primary Button - Purple to Magenta */
    .btn-primary-alt {
        background: linear-gradient(135deg, #7C3AED 0%, #EC4899 100%);
        color: white;
        padding: 16px 32px;
        border: none;
        border-radius: 10px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        min-width: 180px;
        box-shadow: 0 4px 14px rgba(124, 58, 237, 0.3);
        position: relative;
        overflow: hidden;
    }
    
    .btn-primary-alt::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #5B21B6 0%, #BE185D 100%);
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .btn-primary-alt:hover {
        transform: scale(1.05);
        box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
    }
    
    .btn-primary-alt:hover::before {
        opacity: 1;
    }
    
    .btn-primary-alt span {
        position: relative;
        z-index: 1;
    }
    
    .btn-primary-alt .icon {
        position: relative;
        z-index: 1;
        font-size: 18px;
    }
    
    /* Streamlit Button Override for Primary */
    .stButton > button[data-testid="primary"] {
        background: linear-gradient(135deg, #2563EB 0%, #0891B2 100%) !important;
        color: white !important;
        padding: 16px 32px !important;
        border: none !important;
        border-radius: 10px !important;
        font-family: 'Inter', sans-serif !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        box-shadow: 0 4px 14px rgba(37, 99, 235, 0.3) !important;
        width: 100% !important;
        min-height: 56px !important;
    }
    
    .stButton > button[data-testid="primary"]:hover {
        transform: scale(1.05) !important;
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.4) !important;
        background: linear-gradient(135deg, #1D4ED8 0%, #0E7490 100%) !important;
    }
    
    /* Streamlit Button Override for Secondary */
    .stButton > button[data-testid="secondary"] {
        background: white !important;
        color: #374151 !important;
        padding: 16px 32px !important;
        border: 2px solid #D1D5DB !important;
        border-radius: 10px !important;
        font-family: 'Inter', sans-serif !important;
        font-size: 16px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        width: 100% !important;
        min-height: 56px !important;
    }
    
    .stButton > button[data-testid="secondary"]:hover {
        background: #F3F4F6 !important;
        border-color: #9CA3AF !important;
        color: #1F2937 !important;
        transform: translateY(-1px) !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .button-showcase {
            padding: 2rem 1rem;
            margin: 1rem;
        }
        
        .cta-button-container {
            flex-direction: column;
            gap: 1rem;
        }
        
        .btn-primary-modern,
        .btn-primary-alt,
        .btn-secondary-modern {
            width: 100%;
            min-width: auto;
            padding: 18px 24px;
            font-size: 16px;
        }
        
        .btn-primary-modern:hover,
        .btn-primary-alt:hover {
            transform: scale(1.02);
        }
    }
    
    /* Accessibility */
    .btn-primary-modern:focus,
    .btn-primary-alt:focus,
    .btn-secondary-modern:focus {
        outline: 2px solid #2563EB;
        outline-offset: 2px;
    }
    
    /* Typography */
    .showcase-title {
        font-family: 'Poppins', sans-serif;
        font-size: 2.5rem;
        font-weight: 700;
        color: #1F2937;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .showcase-subtitle {
        font-family: 'Inter', sans-serif;
        font-size: 1.1rem;
        color: #6B7280;
        text-align: center;
        margin-bottom: 3rem;
        line-height: 1.6;
    }
    
    .section-title {
        font-family: 'Poppins', sans-serif;
        font-size: 1.5rem;
        font-weight: 600;
        color: #374151;
        margin: 3rem 0 1rem 0;
        text-align: center;
    }
    
    .specs-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin: 2rem 0;
    }
    
    .spec-card {
        background: #F9FAFB;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid #2563EB;
    }
    
    .spec-title {
        font-weight: 600;
        color: #1F2937;
        margin-bottom: 0.5rem;
    }
    
    .spec-detail {
        color: #6B7280;
        font-size: 0.9rem;
        line-height: 1.5;
    }
    </style>
    """, unsafe_allow_html=True)

def create_button_showcase():
    """Create the main button showcase"""
    st.markdown("""
    <div class="button-showcase">
        <h1 class="showcase-title">Modern CTA Buttons</h1>
        <p class="showcase-subtitle">
            Professional, accessible call-to-action buttons designed for SkillSwap learning platform
        </p>
        
        <h2 class="section-title">Primary Design - Blue to Teal Gradient</h2>
        <div class="cta-button-container">
            <button class="btn-primary-modern">
                <span class="icon">🚀</span>
                <span>Start Your Journey</span>
            </button>
            <button class="btn-secondary-modern">
                <span class="icon">🔑</span>
                <span>Login</span>
            </button>
        </div>
        
        <h2 class="section-title">Alternative Design - Purple to Magenta Gradient</h2>
        <div class="cta-button-container">
            <button class="btn-primary-alt">
                <span class="icon">🚀</span>
                <span>Get Started Now</span>
            </button>
            <button class="btn-secondary-modern">
                <span class="icon">🔑</span>
                <span>Sign In</span>
            </button>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_specifications():
    """Create design specifications"""
    st.markdown("""
    <div class="button-showcase">
        <h2 class="section-title">Design Specifications</h2>
        
        <div class="specs-grid">
            <div class="spec-card">
                <div class="spec-title">🎨 Primary Button Colors</div>
                <div class="spec-detail">
                    <strong>Blue-Teal Gradient:</strong><br>
                    Start: #2563EB (Blue 600)<br>
                    End: #0891B2 (Cyan 600)<br>
                    Hover: #1D4ED8 → #0E7490<br><br>
                    
                    <strong>Purple-Magenta Gradient:</strong><br>
                    Start: #7C3AED (Violet 600)<br>
                    End: #EC4899 (Pink 500)<br>
                    Hover: #5B21B6 → #BE185D
                </div>
            </div>
            
            <div class="spec-card">
                <div class="spec-title">🔘 Secondary Button Colors</div>
                <div class="spec-detail">
                    Background: #FFFFFF (White)<br>
                    Border: #D1D5DB (Gray 300)<br>
                    Text: #374151 (Gray 700)<br>
                    Hover BG: #F3F4F6 (Gray 100)<br>
                    Hover Border: #9CA3AF (Gray 400)
                </div>
            </div>
            
            <div class="spec-card">
                <div class="spec-title">📝 Typography</div>
                <div class="spec-detail">
                    Font Family: Inter (sans-serif)<br>
                    Primary Size: 16px<br>
                    Primary Weight: 600 (Semi-bold)<br>
                    Secondary Weight: 500 (Medium)<br>
                    Line Height: 1.5
                </div>
            </div>
            
            <div class="spec-card">
                <div class="spec-title">📐 Dimensions</div>
                <div class="spec-detail">
                    Border Radius: 10px<br>
                    Padding: 16px 32px<br>
                    Min Width: 180px (Primary), 140px (Secondary)<br>
                    Gap: 8px (icon to text)<br>
                    Container Gap: 1.5rem
                </div>
            </div>
            
            <div class="spec-card">
                <div class="spec-title">✨ Animations</div>
                <div class="spec-detail">
                    Transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1)<br>
                    Primary Hover: scale(1.05)<br>
                    Secondary Hover: translateY(-1px)<br>
                    Shadow Enhancement: 0.3s ease
                </div>
            </div>
            
            <div class="spec-card">
                <div class="spec-title">♿ Accessibility</div>
                <div class="spec-detail">
                    WCAG AA Compliant Contrast<br>
                    Focus Outline: 2px solid #2563EB<br>
                    Touch Target: 44px minimum<br>
                    Keyboard Navigation Support
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_streamlit_integration():
    """Show Streamlit integration example"""
    st.markdown("""
    <div class="button-showcase">
        <h2 class="section-title">Streamlit Integration Example</h2>
        <p class="showcase-subtitle">
            These buttons work seamlessly with Streamlit's button components
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("🚀 Start Your Journey", key="primary_demo", help="Primary CTA button"):
                st.success("🎉 Welcome to SkillSwap! Let's get you started.")
        
        with btn_col2:
            if st.button("🔑 Login", key="secondary_demo", help="Secondary login button"):
                st.info("👋 Welcome back! Please enter your credentials.")

def create_mobile_preview():
    """Create mobile preview section"""
    st.markdown("""
    <div class="button-showcase">
        <h2 class="section-title">📱 Mobile Responsive Design</h2>
        <p class="showcase-subtitle">
            Buttons automatically adapt to mobile screens with full-width layout and optimized touch targets
        </p>
        
        <div style="max-width: 375px; margin: 2rem auto; padding: 1rem; background: #F3F4F6; border-radius: 12px; border: 2px dashed #D1D5DB;">
            <div style="text-align: center; margin-bottom: 1rem; color: #6B7280; font-size: 0.9rem;">
                📱 Mobile Preview (375px width)
            </div>
            <div style="display: flex; flex-direction: column; gap: 1rem;">
                <button class="btn-primary-modern" style="width: 100%; min-width: auto;">
                    <span class="icon">🚀</span>
                    <span>Start Your Journey</span>
                </button>
                <button class="btn-secondary-modern" style="width: 100%; min-width: auto;">
                    <span class="icon">🔑</span>
                    <span>Login</span>
                </button>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def main():
    """Main application function"""
    load_modern_button_styles()
    
    create_button_showcase()
    create_specifications()
    create_streamlit_integration()
    create_mobile_preview()
    
    # Footer
    st.markdown("""
    <div class="button-showcase">
        <div style="text-align: center; padding: 2rem 0; border-top: 1px solid #E5E7EB; margin-top: 2rem;">
            <p style="color: #6B7280; margin: 0;">
                Modern CTA Buttons for SkillSwap Learning Platform<br>
                <small>Designed with accessibility, performance, and user experience in mind</small>
            </p>
        </div>
    </div>
    """, unsafe_allow_html=True)

if __name__ == "__main__":
    main()
