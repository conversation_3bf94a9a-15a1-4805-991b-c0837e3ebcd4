import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime, timedelta
import hashlib
import time

# 🎨 DESIGN SYSTEM
# Color Palette: Learning-focused, warm, trustworthy
PRIMARY_COLOR = "#6366F1"      # Indigo - trust, learning
SECONDARY_COLOR = "#10B981"    # Emerald - growth, success
ACCENT_COLOR = "#F59E0B"       # Amber - energy, creativity
BACKGROUND_COLOR = "#F8FAFC"   # Slate-50 - clean, minimal
TEXT_COLOR = "#1E293B"         # Slate-800 - readable
MUTED_COLOR = "#64748B"        # Slate-500 - secondary text
SUCCESS_COLOR = "#059669"      # Emerald-600 - positive actions
WARNING_COLOR = "#D97706"      # Amber-600 - attention

# Page configuration with premium feel
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🌱",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for premium feel
st.markdown(f"""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {{
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, {BACKGROUND_COLOR} 0%, #E2E8F0 100%);
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    
    /* Custom Header */
    .custom-header {{
        background: linear-gradient(90deg, {PRIMARY_COLOR} 0%, {SECONDARY_COLOR} 100%);
        padding: 1rem 2rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }}
    
    .header-title {{
        color: white;
        font-size: 2rem;
        font-weight: 700;
        margin: 0;
        text-align: center;
    }}
    
    .header-subtitle {{
        color: rgba(255, 255, 255, 0.9);
        font-size: 1.1rem;
        text-align: center;
        margin-top: 0.5rem;
    }}
    
    /* Premium Cards */
    .premium-card {{
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
        border: 1px solid #E2E8F0;
        margin-bottom: 1.5rem;
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }}
    
    .premium-card:hover {{
        transform: translateY(-2px);
        box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
    }}
    
    /* Trust Signals */
    .trust-badge {{
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg, {SUCCESS_COLOR}, {SECONDARY_COLOR});
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
    }}
    
    .stats-container {{
        display: flex;
        justify-content: space-around;
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin: 1rem 0;
    }}
    
    .stat-item {{
        text-align: center;
    }}
    
    .stat-number {{
        font-size: 2rem;
        font-weight: 700;
        color: {PRIMARY_COLOR};
        display: block;
    }}
    
    .stat-label {{
        font-size: 0.875rem;
        color: {MUTED_COLOR};
        margin-top: 0.25rem;
    }}
    
    /* Testimonial Cards */
    .testimonial {{
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid {ACCENT_COLOR};
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }}
    
    .testimonial-text {{
        font-style: italic;
        color: {TEXT_COLOR};
        margin-bottom: 1rem;
    }}
    
    .testimonial-author {{
        font-weight: 600;
        color: {PRIMARY_COLOR};
    }}
    
    /* Skill Tags */
    .skill-tag {{
        display: inline-block;
        background: linear-gradient(135deg, {PRIMARY_COLOR}20, {SECONDARY_COLOR}20);
        color: {PRIMARY_COLOR};
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
        border: 1px solid {PRIMARY_COLOR}40;
    }}
    
    /* Premium Button */
    .premium-button {{
        background: linear-gradient(135deg, {PRIMARY_COLOR}, {SECONDARY_COLOR});
        color: white;
        padding: 1rem 2rem;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        width: 100%;
        text-align: center;
        text-decoration: none;
        display: block;
    }}
    
    .premium-button:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .custom-header {{
            padding: 1rem;
        }}
        
        .header-title {{
            font-size: 1.5rem;
        }}
        
        .premium-card {{
            padding: 1.5rem;
        }}
        
        .stats-container {{
            flex-direction: column;
            gap: 1rem;
        }}
    }}
    
    /* Form Styling */
    .stTextInput > div > div > input {{
        border-radius: 8px;
        border: 2px solid #E2E8F0;
        padding: 0.75rem;
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }}
    
    .stTextInput > div > div > input:focus {{
        border-color: {PRIMARY_COLOR};
        box-shadow: 0 0 0 3px {PRIMARY_COLOR}20;
    }}
    
    .stSelectbox > div > div > select {{
        border-radius: 8px;
        border: 2px solid #E2E8F0;
        padding: 0.75rem;
    }}
    
    /* Success Messages */
    .success-message {{
        background: linear-gradient(135deg, {SUCCESS_COLOR}10, {SECONDARY_COLOR}10);
        border: 1px solid {SUCCESS_COLOR}30;
        border-radius: 8px;
        padding: 1rem;
        color: {SUCCESS_COLOR};
        font-weight: 500;
    }}
</style>
""", unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "welcome"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False

# Trust signals and social proof
def show_trust_signals():
    st.markdown("""
    <div class="stats-container">
        <div class="stat-item">
            <span class="stat-number">2,847</span>
            <div class="stat-label">Active Learners</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">1,293</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-item">
            <span class="stat-number">89%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def show_testimonials():
    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months!",
            "author": "Rahul K., Software Developer",
            "skills": "React ↔ Digital Marketing"
        },
        {
            "text": "The community here is incredible. I've made 3 skill exchanges and gained 2 lifelong friends.",
            "author": "Sneha M., Designer",
            "skills": "UI/UX ↔ Content Writing"
        },
        {
            "text": "Best ₹49 I spend every month. The quality of matches is outstanding.",
            "author": "Arjun P., Data Analyst",
            "skills": "Python ↔ Photography"
        }
    ]
    
    for testimonial in testimonials:
        st.markdown(f"""
        <div class="testimonial">
            <div class="testimonial-text">"{testimonial['text']}"</div>
            <div class="testimonial-author">{testimonial['author']}</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.875rem; margin-top: 0.5rem;">
                {testimonial['skills']}
            </div>
        </div>
        """, unsafe_allow_html=True)

def show_popular_skills():
    skills = [
        "Web Development", "Digital Marketing", "Data Science", "UI/UX Design",
        "Content Writing", "Photography", "Python", "React", "SEO", "Graphic Design",
        "Video Editing", "Social Media", "Excel", "Public Speaking", "Language Learning"
    ]
    
    st.markdown("### 🔥 Popular Skills Being Exchanged")
    
    # Create skill tags
    skills_html = ""
    for skill in skills:
        skills_html += f'<span class="skill-tag">{skill}</span>'
    
    st.markdown(f'<div style="margin: 1rem 0;">{skills_html}</div>', unsafe_allow_html=True)

# Main application
def main():
    init_session_state()
    
    # Custom header
    st.markdown(f"""
    <div class="custom-header">
        <h1 class="header-title">🌱 SkillSwap</h1>
        <p class="header-subtitle">Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Page routing
    if st.session_state.page == "welcome":
        show_welcome_page()
    elif st.session_state.page == "onboarding":
        show_onboarding_page()
    elif st.session_state.page == "payment":
        show_payment_page()
    elif st.session_state.page == "dashboard":
        show_dashboard_page()

def show_welcome_page():
    """Welcome page with value proposition and social proof"""

    # Hero section
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown(f"""
        <div class="premium-card">
            <h2 style="color: {PRIMARY_COLOR}; font-size: 2.5rem; margin-bottom: 1rem;">
                Exchange Skills.<br>Build Connections.<br>Grow Together.
            </h2>
            <p style="font-size: 1.2rem; color: {MUTED_COLOR}; margin-bottom: 2rem;">
                Join 2,800+ learners who are teaching what they know and learning what they love.
                Quality matches, real connections, guaranteed growth.
            </p>

            <div style="margin-bottom: 2rem;">
                <span class="trust-badge">✅ Verified Community</span>
                <span class="trust-badge">🎯 Perfect Matches</span>
                <span class="trust-badge">💬 Direct Contact</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("🚀 Start Your Learning Journey", key="start_journey", help="Join the community"):
            st.session_state.page = "onboarding"
            st.rerun()

    with col2:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <h3 style="color: {PRIMARY_COLOR};">How It Works</h3>
            <div style="margin: 1.5rem 0;">
                <div style="margin: 1rem 0;">
                    <div style="background: {PRIMARY_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">1</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Share your skills</p>
                </div>
                <div style="margin: 1rem 0;">
                    <div style="background: {SECONDARY_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">2</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Get perfect matches</p>
                </div>
                <div style="margin: 1rem 0;">
                    <div style="background: {ACCENT_COLOR}; color: white; width: 40px; height: 40px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: 0.5rem;">3</div>
                    <p style="margin: 0.5rem 0; color: {TEXT_COLOR};">Start learning together</p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Trust signals
    show_trust_signals()

    # Popular skills
    show_popular_skills()

    # Testimonials
    st.markdown("### 💬 What Our Community Says")
    show_testimonials()

    # Pricing preview
    st.markdown(f"""
    <div class="premium-card" style="text-align: center; background: linear-gradient(135deg, {PRIMARY_COLOR}10, {SECONDARY_COLOR}10);">
        <h3 style="color: {PRIMARY_COLOR};">Simple, Transparent Pricing</h3>
        <div style="font-size: 3rem; font-weight: 700; color: {PRIMARY_COLOR}; margin: 1rem 0;">₹49</div>
        <p style="color: {MUTED_COLOR}; margin-bottom: 2rem;">per month • Cancel anytime</p>

        <div style="text-align: left; max-width: 300px; margin: 0 auto;">
            <p style="margin: 0.5rem 0;">✅ Unlimited skill matches</p>
            <p style="margin: 0.5rem 0;">✅ Direct contact information</p>
            <p style="margin: 0.5rem 0;">✅ Priority matching algorithm</p>
            <p style="margin: 0.5rem 0;">✅ Community support</p>
            <p style="margin: 0.5rem 0;">✅ Success tracking</p>
        </div>
    </div>
    """, unsafe_allow_html=True)

def show_onboarding_page():
    """Improved skill input form with better UX"""

    st.markdown(f"""
    <div class="premium-card">
        <h2 style="color: {PRIMARY_COLOR}; text-align: center; margin-bottom: 2rem;">
            Tell Us About Your Skills 🎯
        </h2>
        <p style="text-align: center; color: {MUTED_COLOR}; margin-bottom: 2rem;">
            Help us find your perfect learning partners. The more specific you are, the better your matches!
        </p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("skill_form", clear_on_submit=False):
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("### 👤 About You")
            name = st.text_input(
                "Full Name *",
                placeholder="e.g., Priya Sharma",
                help="This will be visible to your matches"
            )
            email = st.text_input(
                "Email Address *",
                placeholder="<EMAIL>",
                help="We'll send you match notifications here"
            )

            st.markdown("### 🎓 What You Can Teach")
            skill_have = st.text_input(
                "Your Expertise *",
                placeholder="e.g., React Development, Digital Marketing, Photography",
                help="Be specific! 'React Development' is better than 'Programming'"
            )

            skill_level_teach = st.selectbox(
                "Your Level in This Skill",
                ["Beginner (1-2 years)", "Intermediate (2-5 years)", "Advanced (5+ years)", "Expert (Teaching/Professional)"],
                index=1
            )

        with col2:
            st.markdown("### 📚 What You Want to Learn")
            skill_want = st.text_input(
                "Skill You Want to Learn *",
                placeholder="e.g., UI/UX Design, Content Writing, Python",
                help="What skill would help you grow professionally or personally?"
            )

            skill_level_learn = st.selectbox(
                "Your Current Level",
                ["Complete Beginner", "Some Knowledge", "Intermediate", "Need Advanced Help"],
                index=0
            )

            st.markdown("### ⏰ Your Availability")
            availability = st.selectbox(
                "When Can You Meet? *",
                [
                    "Weekends Only",
                    "Weekday Evenings (6-9 PM)",
                    "Flexible Schedule",
                    "Weekday Mornings",
                    "Custom Schedule"
                ]
            )

            if availability == "Custom Schedule":
                custom_availability = st.text_input(
                    "Specify Your Availability",
                    placeholder="e.g., Tuesdays and Thursdays 7-9 PM"
                )

        # Learning goals
        st.markdown("### 🎯 Your Learning Goals (Optional)")
        learning_goal = st.text_area(
            "What do you hope to achieve?",
            placeholder="e.g., I want to transition to a UX role, build a portfolio, or start freelancing",
            height=100
        )

        # Commitment level
        st.markdown("### 💪 Your Commitment")
        commitment = st.radio(
            "How much time can you dedicate weekly?",
            ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
            horizontal=True
        )

        # Terms and submit
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            terms_agreed = st.checkbox(
                "I agree to the Terms of Service and Community Guidelines",
                help="By joining, you commit to respectful learning and teaching"
            )

            submitted = st.form_submit_button(
                "🌟 Join the Community",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([name, email, skill_have, skill_want, availability, terms_agreed]):
                st.error("Please fill in all required fields and agree to the terms.")
            elif "@" not in email:
                st.error("Please enter a valid email address.")
            else:
                # Store user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability

                st.session_state.user_data = {
                    'name': name,
                    'email': email,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'skill_level_teach': skill_level_teach,
                    'skill_level_learn': skill_level_learn,
                    'availability': final_availability,
                    'learning_goal': learning_goal,
                    'commitment': commitment,
                    'bio': f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. {learning_goal}"
                }

                st.session_state.page = "payment"
                st.rerun()

def show_payment_page():
    """Premium payment onboarding with value reinforcement"""

    user_data = st.session_state.user_data

    st.markdown(f"""
    <div class="premium-card" style="text-align: center;">
        <h2 style="color: {PRIMARY_COLOR}; margin-bottom: 1rem;">
            🎉 Welcome to SkillSwap, {user_data.get('name', 'Friend')}!
        </h2>
        <p style="color: {MUTED_COLOR}; font-size: 1.1rem; margin-bottom: 2rem;">
            You're one step away from connecting with amazing learning partners.
        </p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {PRIMARY_COLOR};">Your Learning Profile</h3>
            <div style="margin: 1rem 0;">
                <strong>Can Teach:</strong> {user_data.get('skill_have', 'N/A')}<br>
                <strong>Wants to Learn:</strong> {user_data.get('skill_want', 'N/A')}<br>
                <strong>Availability:</strong> {user_data.get('availability', 'N/A')}<br>
                <strong>Commitment:</strong> {user_data.get('commitment', 'N/A')} per week
            </div>

            <div style="background: {SUCCESS_COLOR}10; padding: 1rem; border-radius: 8px; border-left: 4px solid {SUCCESS_COLOR};">
                <strong style="color: {SUCCESS_COLOR};">🎯 Potential Matches Found!</strong><br>
                <span style="color: {MUTED_COLOR};">We found 12 people who want to learn {user_data.get('skill_have', 'your skill')}
                and can teach {user_data.get('skill_want', 'what you want to learn')}.</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {PRIMARY_COLOR}; text-align: center;">Unlock Your Matches</h3>

            <div style="text-align: center; margin: 2rem 0;">
                <div style="font-size: 3rem; font-weight: 700; color: {PRIMARY_COLOR};">₹49</div>
                <p style="color: {MUTED_COLOR};">per month • Cancel anytime</p>
            </div>

            <div style="text-align: left;">
                <p style="margin: 0.75rem 0; display: flex; align-items: center;">
                    <span style="color: {SUCCESS_COLOR}; margin-right: 0.5rem;">✅</span>
                    Access to all 12 potential matches
                </p>
                <p style="margin: 0.75rem 0; display: flex; align-items: center;">
                    <span style="color: {SUCCESS_COLOR}; margin-right: 0.5rem;">✅</span>
                    Direct contact information
                </p>
                <p style="margin: 0.75rem 0; display: flex; align-items: center;">
                    <span style="color: {SUCCESS_COLOR}; margin-right: 0.5rem;">✅</span>
                    AI-powered conversation starters
                </p>
                <p style="margin: 0.75rem 0; display: flex; align-items: center;">
                    <span style="color: {SUCCESS_COLOR}; margin-right: 0.5rem;">✅</span>
                    Priority in future matches
                </p>
                <p style="margin: 0.75rem 0; display: flex; align-items: center;">
                    <span style="color: {SUCCESS_COLOR}; margin-right: 0.5rem;">✅</span>
                    Community support & resources
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Payment form
    st.markdown(f"""
    <div class="premium-card">
        <h3 style="color: {PRIMARY_COLOR}; text-align: center;">Complete Your Membership</h3>
    </div>
    """, unsafe_allow_html=True)

    with st.form("payment_form"):
        col1, col2 = st.columns(2)

        with col1:
            payment_name = st.text_input(
                "Full Name for Payment",
                value=user_data.get('name', ''),
                help="As it appears on your payment method"
            )
            payment_email = st.text_input(
                "Email for Receipt",
                value=user_data.get('email', ''),
                help="We'll send your receipt here"
            )

        with col2:
            st.markdown("### 🔒 Secure Payment")
            st.info("💳 We use Razorpay for secure payments\n🔐 Your data is encrypted and safe\n💰 Cancel anytime, no hidden fees")

        # Trust signals
        st.markdown(f"""
        <div style="text-align: center; margin: 1.5rem 0;">
            <span class="trust-badge">🔒 256-bit SSL Encryption</span>
            <span class="trust-badge">💳 Razorpay Secured</span>
            <span class="trust-badge">🛡️ Money-back Guarantee</span>
        </div>
        """, unsafe_allow_html=True)

        terms_payment = st.checkbox(
            "I agree to the payment terms and monthly subscription",
            help="You can cancel anytime from your dashboard"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button(
                "🚀 Start Learning - ₹49/month",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([payment_name, payment_email, terms_payment]):
                st.error("Please fill all fields and agree to terms")
            else:
                # Simulate payment success
                st.success("🎉 Payment successful! Welcome to SkillSwap Premium!")
                st.session_state.authenticated = True
                st.session_state.page = "dashboard"
                time.sleep(2)
                st.rerun()

def show_dashboard_page():
    """Premium dashboard with matches and community feel"""

    user_data = st.session_state.user_data

    # Welcome header
    st.markdown(f"""
    <div class="premium-card" style="background: linear-gradient(135deg, {PRIMARY_COLOR}, {SECONDARY_COLOR}); color: white;">
        <h2 style="margin-bottom: 0.5rem;">Welcome back, {user_data.get('name', 'Friend')}! 👋</h2>
        <p style="opacity: 0.9; margin: 0;">Ready to connect with your learning partners?</p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <div style="font-size: 2rem; color: {PRIMARY_COLOR};">12</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Available Matches</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <div style="font-size: 2rem; color: {SECONDARY_COLOR};">3</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Conversations Started</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <div style="font-size: 2rem; color: {ACCENT_COLOR};">1</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Active Exchange</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <div style="font-size: 2rem; color: {SUCCESS_COLOR};">95%</div>
            <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">Match Quality</div>
        </div>
        """, unsafe_allow_html=True)

    # Main content tabs
    tab1, tab2, tab3 = st.tabs(["🎯 Your Matches", "💬 Conversations", "📊 Progress"])

    with tab1:
        show_matches_section(user_data)

    with tab2:
        show_conversations_section()

    with tab3:
        show_progress_section()

def show_matches_section(user_data):
    """Show potential matches with rich profiles"""

    # Sample matches data
    matches = [
        {
            "name": "Arjun Patel",
            "skill_have": "UI/UX Design",
            "skill_want": "React Development",
            "experience": "3 years",
            "availability": "Weekday Evenings",
            "bio": "Product designer at a fintech startup. Love creating user-centered designs and want to learn frontend development to better collaborate with developers.",
            "match_score": 95,
            "verified": True,
            "rating": 4.9,
            "exchanges": 5
        },
        {
            "name": "Priya Sharma",
            "skill_have": "Content Writing",
            "skill_want": "React Development",
            "experience": "4 years",
            "availability": "Flexible Schedule",
            "bio": "Freelance content writer specializing in tech and SaaS. Want to transition into tech by learning React and building my own projects.",
            "match_score": 88,
            "verified": True,
            "rating": 4.8,
            "exchanges": 3
        },
        {
            "name": "Rohit Kumar",
            "skill_have": "Digital Marketing",
            "skill_want": "React Development",
            "experience": "2 years",
            "availability": "Weekends Only",
            "bio": "Marketing manager looking to upskill in tech. I can teach you everything about SEO, social media marketing, and growth hacking.",
            "match_score": 82,
            "verified": False,
            "rating": 4.7,
            "exchanges": 2
        }
    ]

    st.markdown("### 🎯 Perfect Matches for You")
    st.markdown(f"People who want to learn **{user_data.get('skill_have', 'your skill')}** and can teach **{user_data.get('skill_want', 'what you want')}**")

    for i, match in enumerate(matches):
        with st.container():
            st.markdown(f"""
            <div class="premium-card">
                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
                    <div>
                        <h3 style="color: {PRIMARY_COLOR}; margin: 0; display: flex; align-items: center;">
                            {match['name']}
                            {'<span style="color: ' + SUCCESS_COLOR + '; margin-left: 0.5rem;">✓</span>' if match['verified'] else ''}
                        </h3>
                        <div style="color: {MUTED_COLOR}; font-size: 0.9rem; margin-top: 0.25rem;">
                            ⭐ {match['rating']} • {match['exchanges']} successful exchanges
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div style="background: {SUCCESS_COLOR}; color: white; padding: 0.25rem 0.75rem;
                             border-radius: 12px; font-size: 0.875rem; font-weight: 500;">
                            {match['match_score']}% Match
                        </div>
                    </div>
                </div>

                <div style="margin: 1rem 0;">
                    <div style="display: flex; gap: 1rem; margin-bottom: 1rem;">
                        <div style="flex: 1;">
                            <strong style="color: {SECONDARY_COLOR};">Can Teach:</strong><br>
                            <span style="color: {TEXT_COLOR};">{match['skill_have']} ({match['experience']})</span>
                        </div>
                        <div style="flex: 1;">
                            <strong style="color: {PRIMARY_COLOR};">Wants to Learn:</strong><br>
                            <span style="color: {TEXT_COLOR};">{match['skill_want']}</span>
                        </div>
                    </div>

                    <div style="margin: 1rem 0;">
                        <strong>About:</strong><br>
                        <span style="color: {TEXT_COLOR};">{match['bio']}</span>
                    </div>

                    <div style="color: {MUTED_COLOR}; font-size: 0.9rem;">
                        📅 Available: {match['availability']}
                    </div>
                </div>

                <div style="display: flex; gap: 1rem; margin-top: 1.5rem;">
                    <button style="flex: 1; background: {PRIMARY_COLOR}; color: white; border: none;
                           padding: 0.75rem; border-radius: 8px; font-weight: 500; cursor: pointer;">
                        💬 Start Conversation
                    </button>
                    <button style="flex: 1; background: white; color: {PRIMARY_COLOR}; border: 2px solid {PRIMARY_COLOR};
                           padding: 0.75rem; border-radius: 8px; font-weight: 500; cursor: pointer;">
                        👀 View Full Profile
                    </button>
                </div>
            </div>
            """, unsafe_allow_html=True)

def show_conversations_section():
    """Show active conversations"""
    st.markdown("### 💬 Your Conversations")
    st.info("🚀 Feature coming soon! You'll be able to chat directly with your matches here.")

def show_progress_section():
    """Show learning progress and achievements"""
    st.markdown("### 📊 Your Learning Journey")
    st.info("📈 Track your skill exchanges, learning goals, and achievements here.")

if __name__ == "__main__":
    main()
