import streamlit as st
try:
    import streamlit_option_menu as option_menu
except ImportError:
    option_menu = None
import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime, timedelta
import hashlib
import time
import base64

# Enhanced Design System
DESIGN_SYSTEM = {
    "colors": {
        "primary": "#4F46E5",      # Indigo-600 - Trust, premium
        "secondary": "#059669",    # Emerald-600 - Success, growth
        "accent": "#F59E0B",       # Amber-500 - Energy, highlights
        "neutral_50": "#F9FAFB",   # Light background
        "neutral_100": "#F3F4F6",  # Card backgrounds
        "neutral_500": "#6B7280",  # Muted text
        "neutral_900": "#111827",  # Primary text
        "success": "#10B981",      # Success states
        "warning": "#F59E0B",      # Warning states
        "error": "#EF4444"         # Error states
    },
    "typography": {
        "font_family": "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
        "sizes": {
            "xs": "0.75rem",
            "sm": "0.875rem", 
            "base": "1rem",
            "lg": "1.125rem",
            "xl": "1.25rem",
            "2xl": "1.5rem",
            "3xl": "1.875rem",
            "4xl": "2.25rem"
        }
    },
    "spacing": {
        "xs": "0.25rem",
        "sm": "0.5rem",
        "md": "1rem", 
        "lg": "1.5rem",
        "xl": "2rem",
        "2xl": "3rem"
    },
    "radius": {
        "sm": "0.375rem",
        "md": "0.5rem",
        "lg": "0.75rem",
        "xl": "1rem"
    }
}

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Enhanced CSS with modern design principles
def load_css():
    st.markdown(f"""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {{
        font-family: {DESIGN_SYSTEM['typography']['font_family']};
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['neutral_50']} 0%, #E0E7FF 100%);
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    .stDeployButton {{visibility: hidden;}}
    
    /* Enhanced Header */
    .skillswap-header {{
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['primary']} 0%, {DESIGN_SYSTEM['colors']['secondary']} 100%);
        padding: {DESIGN_SYSTEM['spacing']['xl']};
        border-radius: {DESIGN_SYSTEM['radius']['xl']};
        margin-bottom: {DESIGN_SYSTEM['spacing']['xl']};
        box-shadow: 0 10px 25px -5px rgba(79, 70, 229, 0.2);
        color: white;
        text-align: center;
    }}
    
    .header-title {{
        font-size: {DESIGN_SYSTEM['typography']['sizes']['4xl']};
        font-weight: 700;
        margin: 0 0 {DESIGN_SYSTEM['spacing']['sm']} 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
    
    .header-subtitle {{
        font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};
        opacity: 0.95;
        margin: 0;
        font-weight: 400;
    }}
    
    /* Premium Card System */
    .premium-card {{
        background: white;
        padding: {DESIGN_SYSTEM['spacing']['xl']};
        border-radius: {DESIGN_SYSTEM['radius']['xl']};
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        border: 1px solid {DESIGN_SYSTEM['colors']['neutral_100']};
        margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }}
    
    .premium-card::before {{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, {DESIGN_SYSTEM['colors']['primary']}, {DESIGN_SYSTEM['colors']['secondary']});
    }}
    
    .premium-card:hover {{
        transform: translateY(-4px);
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }}
    
    /* Enhanced Buttons */
    .btn-primary {{
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['primary']}, {DESIGN_SYSTEM['colors']['secondary']});
        color: white;
        padding: {DESIGN_SYSTEM['spacing']['md']} {DESIGN_SYSTEM['spacing']['xl']};
        border: none;
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        font-weight: 600;
        font-size: {DESIGN_SYSTEM['typography']['sizes']['base']};
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 14px 0 rgba(79, 70, 229, 0.3);
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }}
    
    .btn-primary:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(79, 70, 229, 0.4);
    }}
    
    .btn-secondary {{
        background: white;
        color: {DESIGN_SYSTEM['colors']['primary']};
        padding: {DESIGN_SYSTEM['spacing']['md']} {DESIGN_SYSTEM['spacing']['xl']};
        border: 2px solid {DESIGN_SYSTEM['colors']['primary']};
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
    }}
    
    .btn-secondary:hover {{
        background: {DESIGN_SYSTEM['colors']['primary']};
        color: white;
        transform: translateY(-1px);
    }}
    
    /* Trust Badges */
    .trust-badge {{
        display: inline-flex;
        align-items: center;
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['success']}, {DESIGN_SYSTEM['colors']['secondary']});
        color: white;
        padding: {DESIGN_SYSTEM['spacing']['sm']} {DESIGN_SYSTEM['spacing']['md']};
        border-radius: {DESIGN_SYSTEM['radius']['xl']};
        font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']};
        font-weight: 500;
        margin: {DESIGN_SYSTEM['spacing']['xs']};
        box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
    }}
    
    /* Statistics Grid */
    .stats-container {{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: {DESIGN_SYSTEM['spacing']['lg']};
        margin: {DESIGN_SYSTEM['spacing']['xl']} 0;
    }}
    
    .stat-card {{
        background: white;
        padding: {DESIGN_SYSTEM['spacing']['lg']};
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid {DESIGN_SYSTEM['colors']['neutral_100']};
        transition: transform 0.2s ease;
    }}
    
    .stat-card:hover {{
        transform: translateY(-2px);
    }}
    
    .stat-number {{
        font-size: {DESIGN_SYSTEM['typography']['sizes']['3xl']};
        font-weight: 700;
        color: {DESIGN_SYSTEM['colors']['primary']};
        display: block;
        margin-bottom: {DESIGN_SYSTEM['spacing']['sm']};
    }}
    
    .stat-label {{
        color: {DESIGN_SYSTEM['colors']['neutral_500']};
        font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']};
        font-weight: 500;
    }}
    
    /* Profile Cards */
    .profile-card {{
        background: white;
        padding: {DESIGN_SYSTEM['spacing']['lg']};
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid {DESIGN_SYSTEM['colors']['neutral_100']};
        margin-bottom: {DESIGN_SYSTEM['spacing']['md']};
        transition: all 0.2s ease;
    }}
    
    .profile-card:hover {{
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }}
    
    .profile-avatar {{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['primary']}, {DESIGN_SYSTEM['colors']['secondary']});
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};
        margin-bottom: {DESIGN_SYSTEM['spacing']['md']};
    }}
    
    /* Form Enhancements */
    .stTextInput > div > div > input {{
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        border: 2px solid {DESIGN_SYSTEM['colors']['neutral_100']};
        padding: {DESIGN_SYSTEM['spacing']['md']};
        font-size: {DESIGN_SYSTEM['typography']['sizes']['base']};
        transition: all 0.2s ease;
        background: white;
    }}
    
    .stTextInput > div > div > input:focus {{
        border-color: {DESIGN_SYSTEM['colors']['primary']};
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        outline: none;
    }}
    
    .stSelectbox > div > div > select {{
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        border: 2px solid {DESIGN_SYSTEM['colors']['neutral_100']};
        padding: {DESIGN_SYSTEM['spacing']['md']};
        background: white;
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .skillswap-header {{
            padding: {DESIGN_SYSTEM['spacing']['lg']};
        }}
        
        .header-title {{
            font-size: {DESIGN_SYSTEM['typography']['sizes']['2xl']};
        }}
        
        .premium-card {{
            padding: {DESIGN_SYSTEM['spacing']['lg']};
        }}
        
        .stats-container {{
            grid-template-columns: repeat(2, 1fr);
            gap: {DESIGN_SYSTEM['spacing']['md']};
        }}
        
        .btn-primary, .btn-secondary {{
            width: 100%;
            margin-bottom: {DESIGN_SYSTEM['spacing']['sm']};
        }}
    }}
    
    /* Loading States */
    .loading-skeleton {{
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: {DESIGN_SYSTEM['radius']['md']};
    }}
    
    @keyframes loading {{
        0% {{ background-position: 200% 0; }}
        100% {{ background-position: -200% 0; }}
    }}
    
    /* Success States */
    .success-message {{
        background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['success']}10, {DESIGN_SYSTEM['colors']['secondary']}10);
        border: 1px solid {DESIGN_SYSTEM['colors']['success']}30;
        border-radius: {DESIGN_SYSTEM['radius']['lg']};
        padding: {DESIGN_SYSTEM['spacing']['lg']};
        color: {DESIGN_SYSTEM['colors']['success']};
        font-weight: 500;
        margin: {DESIGN_SYSTEM['spacing']['md']} 0;
    }}
    </style>
    """, unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "welcome"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "is_premium" not in st.session_state:
        st.session_state.is_premium = False

# Custom colored header function
def colored_header(label, description="", color_name="blue-70"):
    color_map = {
        "blue-70": DESIGN_SYSTEM['colors']['primary'],
        "green-70": DESIGN_SYSTEM['colors']['secondary'],
        "violet-70": "#8B5CF6",
        "red-70": "#EF4444"
    }
    color = color_map.get(color_name, DESIGN_SYSTEM['colors']['primary'])

    st.markdown(f"""
    <div style="background: linear-gradient(90deg, {color}, {color}80);
         padding: {DESIGN_SYSTEM['spacing']['lg']};
         border-radius: {DESIGN_SYSTEM['radius']['lg']};
         margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
        <h2 style="color: white; margin: 0; font-size: {DESIGN_SYSTEM['typography']['sizes']['2xl']};">{label}</h2>
        {f'<p style="color: rgba(255,255,255,0.9); margin: {DESIGN_SYSTEM["spacing"]["sm"]} 0 0 0;">{description}</p>' if description else ''}
    </div>
    """, unsafe_allow_html=True)

# Enhanced navigation
def show_navigation():
    if st.session_state.authenticated:
        if option_menu:
            selected = option_menu.option_menu(
                menu_title=None,
                options=["Dashboard", "My Matches", "Messages", "Profile", "Settings"],
                icons=["house", "people", "chat", "person", "gear"],
                menu_icon="cast",
                default_index=0,
                orientation="horizontal",
                styles={
                    "container": {"padding": "0!important", "background-color": "transparent"},
                    "icon": {"color": DESIGN_SYSTEM['colors']['primary'], "font-size": "18px"},
                    "nav-link": {
                        "font-size": "16px",
                        "text-align": "center",
                        "margin": "0px",
                        "padding": "12px 16px",
                        "border-radius": "8px",
                        "color": DESIGN_SYSTEM['colors']['neutral_500']
                    },
                    "nav-link-selected": {
                        "background-color": DESIGN_SYSTEM['colors']['primary'],
                        "color": "white"
                    },
                }
            )
            return selected
        else:
            # Fallback navigation without option_menu
            col1, col2, col3, col4, col5 = st.columns(5)
            with col1:
                if st.button("Dashboard", use_container_width=True):
                    return "Dashboard"
            with col2:
                if st.button("My Matches", use_container_width=True):
                    return "My Matches"
            with col3:
                if st.button("Messages", use_container_width=True):
                    return "Messages"
            with col4:
                if st.button("Profile", use_container_width=True):
                    return "Profile"
            with col5:
                if st.button("Settings", use_container_width=True):
                    return "Settings"
            return "Dashboard"  # Default
    return None

# Main application
def main():
    init_session_state()
    load_css()
    
    # Enhanced header
    st.markdown(f"""
    <div class="skillswap-header">
        <h1 class="header-title">SkillSwap</h1>
        <p class="header-subtitle">Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Navigation
    if st.session_state.authenticated:
        selected_page = show_navigation()
        if selected_page == "Dashboard":
            show_dashboard()
        elif selected_page == "My Matches":
            show_matches()
        elif selected_page == "Messages":
            show_messages()
        elif selected_page == "Profile":
            show_profile()
        elif selected_page == "Settings":
            show_settings()
    else:
        # Page routing for non-authenticated users
        if st.session_state.page == "welcome":
            show_welcome_page()
        elif st.session_state.page == "onboarding":
            show_enhanced_onboarding()
        elif st.session_state.page == "payment":
            show_payment_flow()

# Enhanced Trust Signals
def show_trust_signals():
    st.markdown(f"""
    <div class="stats-container">
        <div class="stat-card">
            <span class="stat-number">3,247</span>
            <div class="stat-label">Active Learners</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">1,856</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">92%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Enhanced Welcome Page
def show_welcome_page():
    # Hero Section
    col1, col2 = st.columns([3, 2])

    with col1:
        st.markdown(f"""
        <div class="premium-card">
            <h2 style="color: {DESIGN_SYSTEM['colors']['neutral_900']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['3xl']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
                Exchange Skills.<br>Build Your Career.<br>Grow Your Network.
            </h2>
            <p style="font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']}; color: {DESIGN_SYSTEM['colors']['neutral_500']}; margin-bottom: {DESIGN_SYSTEM['spacing']['xl']}; line-height: 1.6;">
                Join 3,200+ professionals who are teaching what they know and learning what they need.
                Quality matches, verified members, guaranteed results.
            </p>

            <div style="margin-bottom: {DESIGN_SYSTEM['spacing']['xl']};">
                <span class="trust-badge">✓ Verified Community</span>
                <span class="trust-badge">✓ Perfect Matches</span>
                <span class="trust-badge">✓ Direct Contact</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

        if st.button("Start Your Learning Journey", key="start_journey", help="Join the community"):
            st.session_state.page = "onboarding"
            st.rerun()

    with col2:
        st.markdown(f"""
        <div class="premium-card" style="text-align: center;">
            <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">How It Works</h3>
            <div style="margin: {DESIGN_SYSTEM['spacing']['xl']} 0;">
                <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                    <div style="background: {DESIGN_SYSTEM['colors']['primary']}; color: white; width: 50px; height: 50px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: {DESIGN_SYSTEM['spacing']['sm']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};">1</div>
                    <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; color: {DESIGN_SYSTEM['colors']['neutral_900']}; font-weight: 500;">Share your expertise</p>
                </div>
                <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                    <div style="background: {DESIGN_SYSTEM['colors']['secondary']}; color: white; width: 50px; height: 50px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: {DESIGN_SYSTEM['spacing']['sm']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};">2</div>
                    <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; color: {DESIGN_SYSTEM['colors']['neutral_900']}; font-weight: 500;">Get matched with learners</p>
                </div>
                <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                    <div style="background: {DESIGN_SYSTEM['colors']['accent']}; color: white; width: 50px; height: 50px;
                         border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                         font-weight: bold; margin-bottom: {DESIGN_SYSTEM['spacing']['sm']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};">3</div>
                    <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; color: {DESIGN_SYSTEM['colors']['neutral_900']}; font-weight: 500;">Exchange knowledge</p>
                </div>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Trust signals
    show_trust_signals()

    # Testimonials
    show_enhanced_testimonials()

    # Pricing
    show_pricing_section()

# Enhanced Testimonials
def show_enhanced_testimonials():
    colored_header(
        label="What Our Community Says",
        description="Real stories from successful skill exchanges",
        color_name="blue-70"
    )

    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months. The platform made it easy to find the right match.",
            "author": "Rahul Kumar",
            "role": "Software Developer",
            "avatar": "RK",
            "rating": 5
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections. Best investment I've made for my career.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer",
            "avatar": "SP",
            "rating": 5
        },
        {
            "text": "Worth every rupee. The quality of matches is outstanding and the platform is easy to use. Highly recommend for serious learners.",
            "author": "Arjun Singh",
            "role": "Data Analyst",
            "avatar": "AS",
            "rating": 5
        }
    ]

    cols = st.columns(3)
    for i, testimonial in enumerate(testimonials):
        with cols[i]:
            st.markdown(f"""
            <div class="premium-card">
                <div style="display: flex; align-items: center; margin-bottom: {DESIGN_SYSTEM['spacing']['md']};">
                    <div class="profile-avatar" style="width: 50px; height: 50px; margin-right: {DESIGN_SYSTEM['spacing']['md']}; margin-bottom: 0;">
                        {testimonial['avatar']}
                    </div>
                    <div>
                        <div style="font-weight: 600; color: {DESIGN_SYSTEM['colors']['neutral_900']};">{testimonial['author']}</div>
                        <div style="color: {DESIGN_SYSTEM['colors']['neutral_500']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']};">{testimonial['role']}</div>
                        <div style="color: {DESIGN_SYSTEM['colors']['accent']};">{'★' * testimonial['rating']}</div>
                    </div>
                </div>
                <p style="font-style: italic; color: {DESIGN_SYSTEM['colors']['neutral_900']}; line-height: 1.6;">
                    "{testimonial['text']}"
                </p>
            </div>
            """, unsafe_allow_html=True)

# Enhanced Pricing Section
def show_pricing_section():
    st.markdown(f"""
    <div class="premium-card" style="text-align: center; background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['primary']}10, {DESIGN_SYSTEM['colors']['secondary']}10);">
        <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['2xl']};">Simple, Transparent Pricing</h3>
        <div style="font-size: {DESIGN_SYSTEM['typography']['sizes']['4xl']}; font-weight: 700; color: {DESIGN_SYSTEM['colors']['primary']}; margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">₹49</div>
        <p style="color: {DESIGN_SYSTEM['colors']['neutral_500']}; margin-bottom: {DESIGN_SYSTEM['spacing']['xl']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};">per month • Cancel anytime</p>

        <div style="text-align: left; max-width: 400px; margin: 0 auto;">
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: {DESIGN_SYSTEM['spacing']['md']};">
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Unlimited skill matches
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Direct contact information
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Priority matching
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Community support
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Progress tracking
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Verified profiles
                </p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Enhanced Onboarding Form
def show_enhanced_onboarding():
    colored_header(
        label="Tell Us About Your Skills",
        description="Help us find your perfect learning partners",
        color_name="blue-70"
    )

    # Progress indicator
    st.markdown(f"""
    <div style="background: white; padding: {DESIGN_SYSTEM['spacing']['lg']}; border-radius: {DESIGN_SYSTEM['radius']['lg']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: {DESIGN_SYSTEM['spacing']['md']};">
            <span style="font-weight: 600; color: {DESIGN_SYSTEM['colors']['neutral_900']};">Step 1 of 3: Profile Setup</span>
            <span style="color: {DESIGN_SYSTEM['colors']['neutral_500']};">33% Complete</span>
        </div>
        <div style="background: {DESIGN_SYSTEM['colors']['neutral_100']}; height: 8px; border-radius: 4px;">
            <div style="background: linear-gradient(90deg, {DESIGN_SYSTEM['colors']['primary']}, {DESIGN_SYSTEM['colors']['secondary']}); height: 100%; width: 33%; border-radius: 4px;"></div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    with st.form("enhanced_skill_form", clear_on_submit=False):
        # Personal Information Section
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['neutral_900']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Personal Information</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            name = st.text_input(
                "Full Name *",
                placeholder="e.g., Priya Sharma",
                help="This will be visible to your matches"
            )
            email = st.text_input(
                "Email Address *",
                placeholder="<EMAIL>",
                help="We'll send you match notifications here"
            )

        with col2:
            location = st.text_input(
                "Location (Optional)",
                placeholder="e.g., Mumbai, India",
                help="Helps find local learning partners"
            )
            linkedin = st.text_input(
                "LinkedIn Profile (Optional)",
                placeholder="https://linkedin.com/in/yourprofile",
                help="Builds trust with potential matches"
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Skills Section
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['neutral_900']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Skills Exchange</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("#### What You Can Teach")
            skill_have = st.text_input(
                "Your Expertise *",
                placeholder="e.g., React Development, Digital Marketing",
                help="Be specific for better matches"
            )

            skill_level_teach = st.selectbox(
                "Your Experience Level",
                ["Beginner (1-2 years)", "Intermediate (2-5 years)", "Advanced (5+ years)", "Expert (Professional)"],
                index=1
            )

            teaching_format = st.multiselect(
                "Preferred Teaching Format",
                ["One-on-one sessions", "Group workshops", "Project collaboration", "Mentoring", "Code reviews"],
                default=["One-on-one sessions"]
            )

        with col2:
            st.markdown("#### What You Want to Learn")
            skill_want = st.text_input(
                "Skill You Want to Learn *",
                placeholder="e.g., UI/UX Design, Content Writing",
                help="What skill would help you grow professionally?"
            )

            skill_level_learn = st.selectbox(
                "Your Current Level",
                ["Complete Beginner", "Some Knowledge", "Intermediate", "Need Advanced Help"],
                index=0
            )

            learning_format = st.multiselect(
                "Preferred Learning Format",
                ["One-on-one sessions", "Group learning", "Hands-on projects", "Structured curriculum", "Flexible guidance"],
                default=["One-on-one sessions"]
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Availability & Goals Section
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['neutral_900']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Availability & Goals</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            availability = st.selectbox(
                "When Can You Meet? *",
                [
                    "Weekends Only",
                    "Weekday Evenings (6-9 PM)",
                    "Flexible Schedule",
                    "Weekday Mornings",
                    "Custom Schedule"
                ]
            )

            if availability == "Custom Schedule":
                custom_availability = st.text_input(
                    "Specify Your Availability",
                    placeholder="e.g., Tuesdays and Thursdays 7-9 PM"
                )

            commitment = st.radio(
                "Time Commitment (per week)",
                ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
                horizontal=True
            )

        with col2:
            learning_goal = st.text_area(
                "Learning Goals (Optional)",
                placeholder="e.g., I want to transition to a UX role, build a portfolio, or start freelancing",
                height=100
            )

            experience_level = st.selectbox(
                "Overall Professional Experience",
                ["Student", "0-2 years", "2-5 years", "5-10 years", "10+ years"],
                index=2
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Terms and Submit
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            terms_agreed = st.checkbox(
                "I agree to the Terms of Service and Community Guidelines",
                help="By joining, you commit to respectful learning and teaching"
            )

            submitted = st.form_submit_button(
                "Continue to Matching",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([name, email, skill_have, skill_want, availability, terms_agreed]):
                st.error("Please fill in all required fields and agree to the terms.")
            elif "@" not in email:
                st.error("Please enter a valid email address.")
            else:
                # Store enhanced user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability

                st.session_state.user_data = {
                    'name': name,
                    'email': email,
                    'location': location,
                    'linkedin': linkedin,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'skill_level_teach': skill_level_teach,
                    'skill_level_learn': skill_level_learn,
                    'teaching_format': teaching_format,
                    'learning_format': learning_format,
                    'availability': final_availability,
                    'commitment': commitment,
                    'learning_goal': learning_goal,
                    'experience_level': experience_level,
                    'bio': f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. {learning_goal}"
                }

                st.session_state.page = "payment"
                st.rerun()

# Enhanced Payment Flow
def show_payment_flow():
    user_data = st.session_state.user_data

    colored_header(
        label=f"Welcome to SkillSwap, {user_data.get('name', 'Friend')}!",
        description="You're one step away from connecting with amazing learning partners",
        color_name="green-70"
    )

    # Show potential matches preview
    st.markdown(f"""
    <div class="success-message">
        <h4 style="margin: 0 0 {DESIGN_SYSTEM['spacing']['sm']} 0;">🎯 Great News!</h4>
        <p style="margin: 0;">We found <strong>15 potential matches</strong> who want to learn {user_data.get('skill_have', 'your skill')}
        and can teach {user_data.get('skill_want', 'what you want to learn')}.</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Your Learning Profile</h3>
            <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                <p><strong>Can Teach:</strong> {user_data.get('skill_have', 'N/A')}</p>
                <p><strong>Experience Level:</strong> {user_data.get('skill_level_teach', 'N/A')}</p>
                <p><strong>Wants to Learn:</strong> {user_data.get('skill_want', 'N/A')}</p>
                <p><strong>Availability:</strong> {user_data.get('availability', 'N/A')}</p>
                <p><strong>Commitment:</strong> {user_data.get('commitment', 'N/A')} per week</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; text-align: center; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Unlock Your Matches</h3>

            <div style="text-align: center; margin: {DESIGN_SYSTEM['spacing']['xl']} 0;">
                <div style="font-size: {DESIGN_SYSTEM['typography']['sizes']['4xl']}; font-weight: 700; color: {DESIGN_SYSTEM['colors']['primary']};">₹49</div>
                <p style="color: {DESIGN_SYSTEM['colors']['neutral_500']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['lg']};">per month • Cancel anytime</p>
            </div>

            <div style="text-align: left;">
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Access to all 15 potential matches
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Direct contact information
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    AI-powered conversation starters
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Priority in future matches
                </p>
                <p style="margin: {DESIGN_SYSTEM['spacing']['sm']} 0; display: flex; align-items: center;">
                    <span style="color: {DESIGN_SYSTEM['colors']['success']}; margin-right: {DESIGN_SYSTEM['spacing']['sm']};">✓</span>
                    Progress tracking & analytics
                </p>
            </div>
        </div>
        """, unsafe_allow_html=True)

    # Payment form
    with st.form("payment_form"):
        st.markdown(f"""
        <div class="premium-card">
            <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; text-align: center; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">Complete Your Membership</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)

        with col1:
            payment_name = st.text_input(
                "Full Name for Payment",
                value=user_data.get('name', ''),
                help="As it appears on your payment method"
            )
            payment_email = st.text_input(
                "Email for Receipt",
                value=user_data.get('email', ''),
                help="We'll send your receipt here"
            )

        with col2:
            st.markdown("#### Secure Payment")
            st.info("🔒 We use Razorpay for secure payments\n💳 Your data is encrypted and safe\n🔄 Cancel anytime, no hidden fees")

        st.markdown("</div>", unsafe_allow_html=True)

        # Trust signals
        st.markdown(f"""
        <div style="text-align: center; margin: {DESIGN_SYSTEM['spacing']['xl']} 0;">
            <span class="trust-badge">🔒 256-bit SSL Encryption</span>
            <span class="trust-badge">💳 Razorpay Secured</span>
            <span class="trust-badge">🛡️ Money-back Guarantee</span>
        </div>
        """, unsafe_allow_html=True)

        terms_payment = st.checkbox(
            "I agree to the payment terms and monthly subscription",
            help="You can cancel anytime from your dashboard"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button(
                "Start Learning - ₹49/month",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([payment_name, payment_email, terms_payment]):
                st.error("Please fill all fields and agree to terms")
            else:
                st.success("🎉 Payment successful! Welcome to SkillSwap Premium!")
                st.session_state.authenticated = True
                st.session_state.is_premium = True
                time.sleep(2)
                st.rerun()

# Enhanced Dashboard
def show_dashboard():
    user_data = st.session_state.user_data

    # Welcome section
    col1, col2 = st.columns([3, 1])
    with col1:
        st.markdown(f"""
        <div class="premium-card" style="background: linear-gradient(135deg, {DESIGN_SYSTEM['colors']['primary']}, {DESIGN_SYSTEM['colors']['secondary']}); color: white;">
            <h2 style="margin-bottom: {DESIGN_SYSTEM['spacing']['sm']};">Welcome back, {user_data.get('name', 'Friend')}! 👋</h2>
            <p style="opacity: 0.9; margin: 0;">Ready to connect with your learning partners?</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        if st.button("🔍 Find New Matches", type="primary"):
            st.rerun()

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    metrics = [
        {"label": "Available Matches", "value": "15", "color": DESIGN_SYSTEM['colors']['primary']},
        {"label": "Active Conversations", "value": "3", "color": DESIGN_SYSTEM['colors']['secondary']},
        {"label": "Skills Learned", "value": "2", "color": DESIGN_SYSTEM['colors']['accent']},
        {"label": "Match Success Rate", "value": "95%", "color": DESIGN_SYSTEM['colors']['success']}
    ]

    for i, metric in enumerate(metrics):
        with [col1, col2, col3, col4][i]:
            st.markdown(f"""
            <div class="stat-card">
                <span class="stat-number" style="color: {metric['color']};">{metric['value']}</span>
                <div class="stat-label">{metric['label']}</div>
            </div>
            """, unsafe_allow_html=True)

    # Recent activity and quick actions
    col1, col2 = st.columns([2, 1])

    with col1:
        colored_header(
            label="Recent Activity",
            description="Your latest interactions and updates",
            color_name="blue-70"
        )

        activities = [
            {"type": "match", "text": "New match found: Arjun wants to learn React", "time": "2 hours ago"},
            {"type": "message", "text": "Priya sent you a message about UI/UX session", "time": "1 day ago"},
            {"type": "session", "text": "Completed session with Rohit on Digital Marketing", "time": "3 days ago"}
        ]

        for activity in activities:
            icon = "🎯" if activity['type'] == 'match' else "💬" if activity['type'] == 'message' else "✅"
            st.markdown(f"""
            <div style="background: white; padding: {DESIGN_SYSTEM['spacing']['md']}; border-radius: {DESIGN_SYSTEM['radius']['md']};
                 margin-bottom: {DESIGN_SYSTEM['spacing']['sm']}; border-left: 4px solid {DESIGN_SYSTEM['colors']['primary']};">
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: {DESIGN_SYSTEM['spacing']['md']};">{icon}</span>
                    <div style="flex: 1;">
                        <p style="margin: 0; color: {DESIGN_SYSTEM['colors']['neutral_900']};">{activity['text']}</p>
                        <small style="color: {DESIGN_SYSTEM['colors']['neutral_500']};">{activity['time']}</small>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

    with col2:
        colored_header(
            label="Quick Actions",
            description="",
            color_name="green-70"
        )

        if st.button("📝 Update Profile", use_container_width=True):
            st.info("Profile update feature coming soon!")

        if st.button("💬 Send Message", use_container_width=True):
            st.info("Messaging feature coming soon!")

        if st.button("📊 View Analytics", use_container_width=True):
            st.info("Analytics dashboard coming soon!")

        if st.button("⚙️ Settings", use_container_width=True):
            st.info("Settings page coming soon!")

# Enhanced Matches Page
def show_matches():
    colored_header(
        label="Your Perfect Matches",
        description="People who want to learn what you teach and can teach what you want to learn",
        color_name="blue-70"
    )

    # Filter options
    col1, col2, col3 = st.columns(3)
    with col1:
        skill_filter = st.selectbox("Filter by Skill", ["All Skills", "React", "UI/UX", "Digital Marketing", "Python"])
    with col2:
        availability_filter = st.selectbox("Availability", ["All Times", "Weekends", "Evenings", "Flexible"])
    with col3:
        experience_filter = st.selectbox("Experience Level", ["All Levels", "Beginner", "Intermediate", "Advanced"])

    # Sample matches with enhanced profiles
    matches = [
        {
            "name": "Arjun Patel",
            "avatar": "AP",
            "skill_have": "UI/UX Design",
            "skill_want": "React Development",
            "experience": "3 years",
            "availability": "Weekday Evenings",
            "location": "Mumbai, India",
            "bio": "Product designer at a fintech startup. Love creating user-centered designs and want to learn frontend development to better collaborate with developers.",
            "match_score": 95,
            "verified": True,
            "rating": 4.9,
            "exchanges": 5,
            "linkedin": "https://linkedin.com/in/arjunpatel",
            "skills_taught": ["Figma", "User Research", "Prototyping"],
            "learning_goals": "Build full-stack applications"
        },
        {
            "name": "Priya Sharma",
            "avatar": "PS",
            "skill_have": "Content Writing",
            "skill_want": "React Development",
            "experience": "4 years",
            "availability": "Flexible Schedule",
            "location": "Delhi, India",
            "bio": "Freelance content writer specializing in tech and SaaS. Want to transition into tech by learning React and building my own projects.",
            "match_score": 88,
            "verified": True,
            "rating": 4.8,
            "exchanges": 3,
            "linkedin": "https://linkedin.com/in/priyasharma",
            "skills_taught": ["Technical Writing", "SEO", "Content Strategy"],
            "learning_goals": "Career transition to tech"
        }
    ]

    for match in matches:
        st.markdown(f"""
        <div class="profile-card">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
                <div style="display: flex; align-items: center;">
                    <div class="profile-avatar" style="margin-right: {DESIGN_SYSTEM['spacing']['md']};">
                        {match['avatar']}
                    </div>
                    <div>
                        <h3 style="color: {DESIGN_SYSTEM['colors']['primary']}; margin: 0; display: flex; align-items: center;">
                            {match['name']}
                            {'<span style="color: ' + DESIGN_SYSTEM['colors']['success'] + '; margin-left: ' + DESIGN_SYSTEM['spacing']['sm'] + ';">✓ Verified</span>' if match['verified'] else ''}
                        </h3>
                        <div style="color: {DESIGN_SYSTEM['colors']['neutral_500']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']}; margin-top: {DESIGN_SYSTEM['spacing']['xs']};">
                            ⭐ {match['rating']} • {match['exchanges']} successful exchanges • {match['location']}
                        </div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="background: {DESIGN_SYSTEM['colors']['success']}; color: white; padding: {DESIGN_SYSTEM['spacing']['xs']} {DESIGN_SYSTEM['spacing']['md']};
                         border-radius: {DESIGN_SYSTEM['radius']['xl']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']}; font-weight: 600;">
                        {match['match_score']}% Match
                    </div>
                </div>
            </div>

            <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: {DESIGN_SYSTEM['spacing']['lg']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
                    <div>
                        <strong style="color: {DESIGN_SYSTEM['colors']['secondary']};">Can Teach:</strong><br>
                        <span style="color: {DESIGN_SYSTEM['colors']['neutral_900']};">{match['skill_have']} ({match['experience']})</span><br>
                        <small style="color: {DESIGN_SYSTEM['colors']['neutral_500']};">Skills: {', '.join(match['skills_taught'])}</small>
                    </div>
                    <div>
                        <strong style="color: {DESIGN_SYSTEM['colors']['primary']};">Wants to Learn:</strong><br>
                        <span style="color: {DESIGN_SYSTEM['colors']['neutral_900']};">{match['skill_want']}</span><br>
                        <small style="color: {DESIGN_SYSTEM['colors']['neutral_500']};">Goal: {match['learning_goals']}</small>
                    </div>
                </div>

                <div style="margin: {DESIGN_SYSTEM['spacing']['lg']} 0;">
                    <strong>About:</strong><br>
                    <span style="color: {DESIGN_SYSTEM['colors']['neutral_900']}; line-height: 1.6;">{match['bio']}</span>
                </div>

                <div style="color: {DESIGN_SYSTEM['colors']['neutral_500']}; font-size: {DESIGN_SYSTEM['typography']['sizes']['sm']}; margin-bottom: {DESIGN_SYSTEM['spacing']['lg']};">
                    📅 Available: {match['availability']} • 🔗 <a href="{match['linkedin']}" target="_blank">LinkedIn Profile</a>
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: {DESIGN_SYSTEM['spacing']['sm']};">
                <button class="btn-primary" style="padding: {DESIGN_SYSTEM['spacing']['sm']} {DESIGN_SYSTEM['spacing']['md']};">
                    💬 Start Chat
                </button>
                <button class="btn-secondary" style="padding: {DESIGN_SYSTEM['spacing']['sm']} {DESIGN_SYSTEM['spacing']['md']};">
                    👀 View Profile
                </button>
                <button style="background: {DESIGN_SYSTEM['colors']['accent']}; color: white; border: none;
                       padding: {DESIGN_SYSTEM['spacing']['sm']} {DESIGN_SYSTEM['spacing']['md']}; border-radius: {DESIGN_SYSTEM['radius']['lg']}; font-weight: 500;">
                    ⭐ Save Match
                </button>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Placeholder pages
def show_messages():
    colored_header(
        label="Messages",
        description="Your conversations with learning partners",
        color_name="green-70"
    )
    st.info("💬 Messaging feature coming soon! You'll be able to chat directly with your matches here.")

def show_profile():
    colored_header(
        label="Your Profile",
        description="Manage your skills and preferences",
        color_name="violet-70"
    )
    st.info("👤 Profile management coming soon! Edit your skills, bio, and preferences here.")

def show_settings():
    colored_header(
        label="Settings",
        description="Manage your account and preferences",
        color_name="red-70"
    )
    st.info("⚙️ Settings page coming soon! Manage notifications, privacy, and billing here.")

if __name__ == "__main__":
    main()
