import streamlit as st

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

def load_premium_theme():
    """Load the premium SkillSwap theme with beautiful backgrounds and responsive design"""
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');
    
    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
        background: #F9FAFB;
        color: #1F2937;
        padding: 0;
    }
    
    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {visibility: hidden;}
    
    /* Premium Header with Gradient */
    .premium-header {
        background: linear-gradient(90deg, #2F80ED, #27AE60);
        padding: 4rem 2rem;
        color: white;
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    /* Abstract background shapes */
    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 300px;
        height: 300px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .premium-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 50%;
    }
    
    .premium-header h1 {
        font-family: 'Poppins', sans-serif;
        font-size: 3.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        position: relative;
        z-index: 2;
    }
    
    .premium-header p {
        font-size: 1.3rem;
        opacity: 0.95;
        margin: 0;
        position: relative;
        z-index: 2;
    }
    
    /* Main Container */
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 64px;
    }
    
    /* Premium Cards */
    .premium-card {
        background: #FFFFFF;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        padding: 32px;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .premium-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    /* Typography */
    .heading-primary {
        font-family: 'Poppins', sans-serif;
        font-size: 2.5rem;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .heading-secondary {
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 600;
        color: #1F2937;
        margin-bottom: 1rem;
    }
    
    .body-text {
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        color: #6B7280;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }
    
    .body-text-large {
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        color: #6B7280;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    /* Premium Buttons */
    .btn-primary {
        background: #2F80ED;
        color: white;
        padding: 16px 32px;
        border: none;
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        box-shadow: 0 2px 8px rgba(47, 128, 237, 0.3);
    }
    
    .btn-primary:hover {
        background: #2368C4;
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.4);
        transform: translateY(-2px);
    }
    
    .btn-secondary {
        background: white;
        color: #2F80ED;
        padding: 16px 32px;
        border: 2px solid #2F80ED;
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
    }
    
    .btn-secondary:hover {
        background: #2F80ED;
        color: white;
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.3);
        transform: translateY(-2px);
    }
    
    /* Streamlit Button Override */
    .stButton > button {
        background: #2F80ED !important;
        color: white !important;
        padding: 16px 32px !important;
        border: none !important;
        border-radius: 12px !important;
        font-family: 'Inter', sans-serif !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(47, 128, 237, 0.3) !important;
        width: 100% !important;
        height: auto !important;
        min-height: 56px !important;
    }
    
    .stButton > button:hover {
        background: #2368C4 !important;
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.4) !important;
        transform: translateY(-2px) !important;
    }
    
    /* Trust Badges */
    .trust-badges {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .trust-badge {
        background: linear-gradient(135deg, #2F80ED, #27AE60);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(47, 128, 237, 0.2);
        transition: all 0.3s ease;
    }
    
    .trust-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.3);
    }
    
    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
    }
    
    .stat-card {
        background: #FFFFFF;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-4px);
    }
    
    .stat-number {
        font-family: 'Poppins', sans-serif;
        font-size: 3rem;
        font-weight: 700;
        color: #2F80ED;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-family: 'Inter', sans-serif;
        color: #6B7280;
        font-size: 16px;
        font-weight: 500;
    }
    
    /* Testimonials */
    .testimonial-card {
        background: #FFFFFF;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        padding: 2rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        border-left: 4px solid #27AE60;
    }
    
    .testimonial-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    .testimonial-text {
        font-style: italic;
        color: #4B5563;
        line-height: 1.6;
        margin-bottom: 1rem;
        font-size: 16px;
    }
    
    .testimonial-author {
        font-weight: 600;
        color: #1F2937;
        margin-bottom: 0.25rem;
    }
    
    .testimonial-role {
        color: #6B7280;
        font-size: 14px;
    }
    
    /* Wave decoration */
    .wave-decoration {
        position: relative;
        overflow: hidden;
    }
    
    .wave-decoration::before {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 60px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' fill='%232F80ED'/%3E%3C/svg%3E") repeat-x;
        background-size: 1200px 60px;
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .main-container {
            padding: 0 16px;
        }
        
        .premium-header {
            padding: 3rem 1rem;
        }
        
        .premium-header h1 {
            font-size: 2.5rem;
        }
        
        .premium-card {
            padding: 24px;
        }
        
        .heading-primary {
            font-size: 2rem;
        }
        
        .heading-secondary {
            font-size: 1.5rem;
        }
        
        .trust-badges {
            gap: 1rem;
        }
        
        .trust-badge {
            font-size: 12px;
            padding: 10px 20px;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .stButton > button {
            font-size: 14px !important;
            padding: 14px 24px !important;
        }
    }
    </style>
    """, unsafe_allow_html=True)

def create_header():
    """Create the premium header with gradient background"""
    st.markdown("""
    <div class="premium-header">
        <h1>SkillSwap</h1>
        <p>Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)

def create_hero_section():
    """Create the main hero section with value proposition"""
    st.markdown("""
    <div class="main-container">
        <div class="premium-card">
            <h2 class="heading-primary">Exchange Skills. Build Your Career. Grow Your Network.</h2>
            <p class="body-text-large">
                Join 3,200+ professionals who are teaching what they know and learning what they need. 
                Quality matches, verified members, guaranteed results.
            </p>
            
            <div class="trust-badges">
                <div class="trust-badge">✓ Verified Community</div>
                <div class="trust-badge">✓ Perfect Matches</div>
                <div class="trust-badge">✓ Direct Contact</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_cta_section():
    """Create the call-to-action section with buttons"""
    st.markdown('<div class="main-container">', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        <div class="premium-card" style="text-align: center;">
            <h3 class="heading-secondary">Choose Your Path</h3>
            <p class="body-text">Join thousands of professionals exchanging skills and growing together</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Side-by-side buttons
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("🚀 Start Your Journey", key="start_journey", help="New to SkillSwap? Sign up here!"):
                st.success("Welcome to SkillSwap! Let's get you started.")
        
        with btn_col2:
            if st.button("👤 Login", key="login", help="Already have an account? Login here!"):
                st.info("Welcome back! Please enter your credentials.")
    
    st.markdown('</div>', unsafe_allow_html=True)

def create_stats_section():
    """Create the statistics section"""
    st.markdown("""
    <div class="main-container">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">3,247</span>
                <div class="stat-label">Active Learners</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" style="color: #27AE60;">1,856</span>
                <div class="stat-label">Skills Exchanged</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">4.9</span>
                <div class="stat-label">Average Rating</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">92%</span>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_testimonials_section():
    """Create the testimonials section"""
    st.markdown("""
    <div class="main-container">
        <h3 class="heading-secondary" style="text-align: center; margin-bottom: 2rem;">What Our Community Says</h3>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns(3)
    
    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months!",
            "author": "Rahul Kumar",
            "role": "Software Developer"
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer"
        },
        {
            "text": "Worth every rupee. The quality of matches is outstanding and the platform is easy to use.",
            "author": "Arjun Singh",
            "role": "Data Analyst"
        }
    ]
    
    for i, testimonial in enumerate(testimonials):
        with [col1, col2, col3][i]:
            st.markdown(f"""
            <div class="main-container">
                <div class="testimonial-card">
                    <p class="testimonial-text">"{testimonial['text']}"</p>
                    <div class="testimonial-author">{testimonial['author']}</div>
                    <div class="testimonial-role">{testimonial['role']}</div>
                    <div style="color: #27AE60; margin-top: 0.5rem;">★★★★★</div>
                </div>
            </div>
            """, unsafe_allow_html=True)

def main():
    """Main application function"""
    # Load the premium theme
    load_premium_theme()
    
    # Create page sections
    create_header()
    create_hero_section()
    create_cta_section()
    create_stats_section()
    create_testimonials_section()
    
    # Add wave decoration at the bottom
    st.markdown('<div class="wave-decoration"></div>', unsafe_allow_html=True)

# Additional utility functions for easy integration

def create_premium_section(title, content, icon=""):
    """Create a premium card section with title and content"""
    st.markdown(f"""
    <div class="main-container">
        <div class="premium-card">
            <h3 class="heading-secondary">{icon} {title}</h3>
            <div class="body-text">{content}</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_feature_grid(features):
    """Create a grid of feature cards"""
    cols = st.columns(len(features))

    for i, feature in enumerate(features):
        with cols[i]:
            st.markdown(f"""
            <div class="premium-card" style="text-align: center; height: 100%;">
                <div style="font-size: 3rem; margin-bottom: 1rem;">{feature.get('icon', '🔥')}</div>
                <h4 style="font-family: 'Poppins', sans-serif; font-weight: 600; color: #1F2937; margin-bottom: 1rem;">
                    {feature['title']}
                </h4>
                <p class="body-text">{feature['description']}</p>
            </div>
            """, unsafe_allow_html=True)

def create_pricing_card(title, price, features, is_popular=False):
    """Create a pricing card"""
    popular_style = """
        border: 2px solid #2F80ED;
        position: relative;
    """ if is_popular else ""

    popular_badge = """
        <div style="position: absolute; top: -12px; left: 50%; transform: translateX(-50%);
             background: #2F80ED; color: white; padding: 6px 20px; border-radius: 20px;
             font-size: 12px; font-weight: 600;">MOST POPULAR</div>
    """ if is_popular else ""

    features_html = "".join([f"<li style='margin-bottom: 0.5rem;'>✓ {feature}</li>" for feature in features])

    st.markdown(f"""
    <div class="premium-card" style="text-align: center; {popular_style}">
        {popular_badge}
        <h3 style="font-family: 'Poppins', sans-serif; font-weight: 600; color: #1F2937; margin-bottom: 1rem;">
            {title}
        </h3>
        <div style="font-size: 3rem; font-weight: 700; color: #2F80ED; margin-bottom: 1rem;">
            {price}
        </div>
        <ul style="list-style: none; padding: 0; margin: 2rem 0; text-align: left;">
            {features_html}
        </ul>
    </div>
    """, unsafe_allow_html=True)

# Example usage and integration guide
def example_integration():
    """Example of how to integrate the theme into your existing app"""

    # 1. Load the theme at the beginning of your app
    load_premium_theme()

    # 2. Create header
    create_header()

    # 3. Create hero section
    create_hero_section()

    # 4. Add your custom content using premium cards
    create_premium_section(
        title="How It Works",
        content="""
        <ol style="padding-left: 1.5rem;">
            <li style="margin-bottom: 1rem;"><strong>Share Your Skills:</strong> Tell us what you can teach and what you want to learn</li>
            <li style="margin-bottom: 1rem;"><strong>Get Matched:</strong> Our algorithm finds perfect learning partners for you</li>
            <li style="margin-bottom: 1rem;"><strong>Start Learning:</strong> Connect directly and begin your skill exchange journey</li>
        </ol>
        """,
        icon="🎯"
    )

    # 5. Add feature grid
    features = [
        {
            "icon": "🎯",
            "title": "Perfect Matches",
            "description": "Our AI algorithm finds the best learning partners based on your skills and goals."
        },
        {
            "icon": "🛡️",
            "title": "Verified Members",
            "description": "All community members are verified to ensure quality and safety."
        },
        {
            "icon": "💬",
            "title": "Direct Contact",
            "description": "Connect directly with your matches without any platform restrictions."
        }
    ]

    st.markdown('<div class="main-container">', unsafe_allow_html=True)
    create_feature_grid(features)
    st.markdown('</div>', unsafe_allow_html=True)

    # 6. Add CTA section
    create_cta_section()

    # 7. Add stats and testimonials
    create_stats_section()
    create_testimonials_section()

if __name__ == "__main__":
    main()
