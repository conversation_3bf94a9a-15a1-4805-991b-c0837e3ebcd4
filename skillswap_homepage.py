import streamlit as st
import pandas as pd
from datetime import datetime
import hashlib
import time

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Design system
COLORS = {
    "primary": "#2563EB",      # Blue-600 - Trust, professional
    "secondary": "#059669",    # Emerald-600 - Growth, success
    "accent": "#F59E0B",       # Amber-500 - Energy, highlights
    "neutral_50": "#F9FAFB",   # Light background
    "neutral_100": "#F3F4F6",  # Card background
    "neutral_500": "#6B7280",  # Muted text
    "neutral_900": "#111827",  # Primary text
    "success": "#10B981",      # Success green
    "error": "#EF4444"         # Error red
}

# Custom CSS for premium look
def load_css():
    st.markdown(f"""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    .main {{
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, {COLORS['neutral_50']} 0%, #E0E7FF 100%);
    }}
    
    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    .stDeployButton {{visibility: hidden;}}
    
    /* Hero Section */
    .hero-container {{
        text-align: center;
        padding: 3rem 2rem;
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }}
    
    .hero-title {{
        font-size: 3rem;
        font-weight: 700;
        color: {COLORS['neutral_900']};
        margin-bottom: 1rem;
        line-height: 1.2;
    }}
    
    .hero-subtitle {{
        font-size: 1.25rem;
        color: {COLORS['neutral_500']};
        margin-bottom: 2rem;
        line-height: 1.6;
    }}
    
    .hero-tagline {{
        font-size: 1rem;
        color: {COLORS['neutral_500']};
        margin-top: 1rem;
    }}
    
    /* CTA Buttons */
    .cta-container {{
        display: flex;
        gap: 1rem;
        justify-content: center;
        margin: 2rem 0;
        flex-wrap: wrap;
    }}
    
    .btn-primary {{
        background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.3);
        text-decoration: none;
        display: inline-block;
        min-width: 200px;
    }}
    
    .btn-primary:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.4);
    }}
    
    .btn-secondary {{
        background: white;
        color: {COLORS['primary']};
        padding: 1rem 2rem;
        border: 2px solid {COLORS['primary']};
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        min-width: 200px;
    }}
    
    .btn-secondary:hover {{
        background: {COLORS['primary']};
        color: white;
        transform: translateY(-1px);
    }}
    
    /* Stats Section */
    .stats-container {{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }}
    
    .stat-card {{
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid {COLORS['neutral_100']};
    }}
    
    .stat-number {{
        font-size: 2.5rem;
        font-weight: 700;
        color: {COLORS['primary']};
        display: block;
        margin-bottom: 0.5rem;
    }}
    
    .stat-label {{
        color: {COLORS['neutral_500']};
        font-size: 0.9rem;
        font-weight: 500;
    }}
    
    /* Form Styling */
    .form-container {{
        background: white;
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }}
    
    .form-title {{
        font-size: 2rem;
        font-weight: 600;
        color: {COLORS['neutral_900']};
        text-align: center;
        margin-bottom: 1rem;
    }}
    
    .form-subtitle {{
        color: {COLORS['neutral_500']};
        text-align: center;
        margin-bottom: 2rem;
    }}
    
    /* Testimonial Cards */
    .testimonial {{
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        border-left: 4px solid {COLORS['primary']};
        margin: 1rem 0;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
    
    .testimonial-text {{
        font-style: italic;
        color: {COLORS['neutral_900']};
        margin-bottom: 1rem;
        line-height: 1.6;
    }}
    
    .testimonial-author {{
        font-weight: 600;
        color: {COLORS['primary']};
    }}
    
    .testimonial-role {{
        color: {COLORS['neutral_500']};
        font-size: 0.9rem;
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .hero-title {{
            font-size: 2rem;
        }}
        
        .hero-subtitle {{
            font-size: 1.1rem;
        }}
        
        .cta-container {{
            flex-direction: column;
            align-items: center;
        }}
        
        .btn-primary, .btn-secondary {{
            width: 100%;
            max-width: 300px;
        }}
        
        .stats-container {{
            grid-template-columns: repeat(2, 1fr);
        }}
    }}
    
    /* Success/Error Messages */
    .success-message {{
        background: linear-gradient(135deg, {COLORS['success']}10, {COLORS['secondary']}10);
        border: 1px solid {COLORS['success']}30;
        border-radius: 8px;
        padding: 1rem;
        color: {COLORS['success']};
        font-weight: 500;
        margin: 1rem 0;
    }}
    
    .error-message {{
        background: linear-gradient(135deg, {COLORS['error']}10, {COLORS['error']}20);
        border: 1px solid {COLORS['error']}30;
        border-radius: 8px;
        padding: 1rem;
        color: {COLORS['error']};
        font-weight: 500;
        margin: 1rem 0;
    }}
    </style>
    """, unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "homepage"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "user_email" not in st.session_state:
        st.session_state.user_email = None

# Navigation function
def navigate_to(page):
    st.session_state.page = page
    st.rerun()

# Homepage
def show_homepage():
    # Hero Section
    st.markdown(f"""
    <div class="hero-container">
        <h1 class="hero-title">SkillSwap</h1>
        <p class="hero-subtitle">Learn Together, Grow Together</p>
        <p style="font-size: 1.1rem; color: {COLORS['neutral_500']}; margin-bottom: 2rem;">
            Join 3,200+ professionals exchanging skills and building careers together
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # CTA Buttons
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown('<div class="cta-container">', unsafe_allow_html=True)
        
        col_a, col_b = st.columns(2)
        with col_a:
            if st.button("🚀 Start Your Journey", key="start_journey", help="Join SkillSwap and find your learning partners"):
                navigate_to("onboarding")
        
        with col_b:
            if st.button("🔑 Login", key="login", help="Access your SkillSwap account"):
                navigate_to("login")
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        st.markdown(f'<p class="hero-tagline">Only ₹49/month • Cancel anytime • 7-day free trial</p>', unsafe_allow_html=True)
    
    # Trust Signals
    show_trust_signals()
    
    # Value Highlights
    show_value_highlights()
    
    # Testimonials
    show_testimonials()

# Trust signals with stats
def show_trust_signals():
    st.markdown(f"""
    <div class="stats-container">
        <div class="stat-card">
            <span class="stat-number">3,247</span>
            <div class="stat-label">Active Members</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">1,856</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">92%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Value highlights section
def show_value_highlights():
    st.markdown("### ✨ Why Choose SkillSwap?")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown(f"""
        <div style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🎯</div>
            <h4 style="color: {COLORS['neutral_900']}; margin-bottom: 0.5rem;">Perfect Matches</h4>
            <p style="color: {COLORS['neutral_500']};">Our algorithm finds people who want to learn what you teach and can teach what you want to learn.</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🤝</div>
            <h4 style="color: {COLORS['neutral_900']}; margin-bottom: 0.5rem;">Direct Connection</h4>
            <p style="color: {COLORS['neutral_500']};">Get direct contact information and start learning immediately. No middleman, no delays.</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div style="text-align: center; padding: 1.5rem;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📈</div>
            <h4 style="color: {COLORS['neutral_900']}; margin-bottom: 0.5rem;">Proven Results</h4>
            <p style="color: {COLORS['neutral_500']};">92% of our members successfully complete their skill exchanges and achieve their learning goals.</p>
        </div>
        """, unsafe_allow_html=True)

# Testimonials section
def show_testimonials():
    st.markdown("### 💬 What Our Members Say")

    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months!",
            "author": "Rahul Kumar",
            "role": "Software Developer"
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer"
        },
        {
            "text": "Best ₹49 I spend every month. The quality of matches is outstanding and the platform is easy to use.",
            "author": "Arjun Singh",
            "role": "Data Analyst"
        }
    ]

    cols = st.columns(3)
    for i, testimonial in enumerate(testimonials):
        with cols[i]:
            st.markdown(f"""
            <div class="testimonial">
                <div class="testimonial-text">"{testimonial['text']}"</div>
                <div class="testimonial-author">{testimonial['author']}</div>
                <div class="testimonial-role">{testimonial['role']}</div>
            </div>
            """, unsafe_allow_html=True)

# Onboarding flow
def show_onboarding():
    st.markdown(f"""
    <div class="form-container">
        <h2 class="form-title">Start Your Learning Journey</h2>
        <p class="form-subtitle">Tell us about yourself and we'll find your perfect learning partners</p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("onboarding_form", clear_on_submit=False):
        # Personal Information
        st.markdown("#### 👤 Personal Information")
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input(
                "Full Name *",
                placeholder="e.g., Priya Sharma",
                help="This will be visible to your matches"
            )
            email = st.text_input(
                "Email Address *",
                placeholder="<EMAIL>",
                help="We'll send you match notifications here"
            )

        with col2:
            location = st.text_input(
                "Location (Optional)",
                placeholder="e.g., Mumbai, India",
                help="Helps find local learning partners"
            )
            experience = st.selectbox(
                "Professional Experience",
                ["Student", "0-2 years", "2-5 years", "5-10 years", "10+ years"],
                index=2
            )

        st.markdown("---")

        # Skills Exchange
        st.markdown("#### 🎓 Skills Exchange")
        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**What You Can Teach**")
            skill_have = st.text_input(
                "Your Expertise *",
                placeholder="e.g., React Development, Digital Marketing",
                help="Be specific for better matches"
            )
            skill_level_teach = st.selectbox(
                "Your Experience Level",
                ["Beginner (1-2 years)", "Intermediate (2-5 years)", "Advanced (5+ years)", "Expert (Professional)"],
                index=1
            )

        with col2:
            st.markdown("**What You Want to Learn**")
            skill_want = st.text_input(
                "Skill You Want to Learn *",
                placeholder="e.g., UI/UX Design, Content Writing",
                help="What skill would help you grow?"
            )
            skill_level_learn = st.selectbox(
                "Your Current Level",
                ["Complete Beginner", "Some Knowledge", "Intermediate", "Need Advanced Help"],
                index=0
            )

        st.markdown("---")

        # Availability
        st.markdown("#### ⏰ Availability")
        col1, col2 = st.columns(2)

        with col1:
            availability = st.selectbox(
                "When Can You Meet? *",
                [
                    "Weekends Only",
                    "Weekday Evenings (6-9 PM)",
                    "Flexible Schedule",
                    "Weekday Mornings",
                    "Custom Schedule"
                ]
            )

            if availability == "Custom Schedule":
                custom_availability = st.text_input(
                    "Specify Your Availability",
                    placeholder="e.g., Tuesdays and Thursdays 7-9 PM"
                )

        with col2:
            commitment = st.radio(
                "Time Commitment (per week)",
                ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
                horizontal=True
            )

        # Learning Goals
        st.markdown("#### 🎯 Learning Goals (Optional)")
        learning_goal = st.text_area(
            "What do you hope to achieve?",
            placeholder="e.g., I want to transition to a UX role, build a portfolio, or start freelancing",
            height=100
        )

        # Terms and Submit
        st.markdown("---")
        terms_agreed = st.checkbox(
            "I agree to the Terms of Service and Community Guidelines",
            help="By joining, you commit to respectful learning and teaching"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button(
                "Find My Matches - ₹49/month",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([name, email, skill_have, skill_want, availability, terms_agreed]):
                st.markdown('<div class="error-message">Please fill in all required fields and agree to the terms.</div>', unsafe_allow_html=True)
            elif "@" not in email:
                st.markdown('<div class="error-message">Please enter a valid email address.</div>', unsafe_allow_html=True)
            else:
                # Store user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability

                st.session_state.user_data = {
                    'name': name,
                    'email': email,
                    'location': location,
                    'experience': experience,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'skill_level_teach': skill_level_teach,
                    'skill_level_learn': skill_level_learn,
                    'availability': final_availability,
                    'commitment': commitment,
                    'learning_goal': learning_goal
                }

                st.markdown('<div class="success-message">🎉 Great! We found potential matches for you. Complete your payment to connect with them.</div>', unsafe_allow_html=True)
                time.sleep(2)
                navigate_to("payment")

# Simple login
def show_login():
    st.markdown(f"""
    <div class="form-container">
        <h2 class="form-title">Welcome Back!</h2>
        <p class="form-subtitle">Enter your email to access your SkillSwap account</p>
    </div>
    """, unsafe_allow_html=True)

    with st.form("login_form"):
        email = st.text_input(
            "Email Address",
            placeholder="<EMAIL>",
            help="The email you used to register"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            login_submitted = st.form_submit_button(
                "Access My Account",
                type="primary",
                use_container_width=True
            )

        if login_submitted:
            if email and "@" in email:
                # Simple email-based login (in production, you'd verify against your database)
                st.session_state.authenticated = True
                st.session_state.user_email = email
                st.markdown('<div class="success-message">✅ Login successful! Redirecting to your dashboard...</div>', unsafe_allow_html=True)
                time.sleep(2)
                navigate_to("dashboard")
            else:
                st.markdown('<div class="error-message">Please enter a valid email address.</div>', unsafe_allow_html=True)

    # Back to homepage
    if st.button("← Back to Homepage"):
        navigate_to("homepage")

# Payment page
def show_payment():
    user_data = st.session_state.user_data

    st.markdown(f"""
    <div class="form-container">
        <h2 class="form-title">🎉 Welcome to SkillSwap, {user_data.get('name', 'Friend')}!</h2>
        <p class="form-subtitle">You're one step away from connecting with amazing learning partners</p>
    </div>
    """, unsafe_allow_html=True)

    # Show matches found
    st.markdown(f"""
    <div class="success-message">
        <h4 style="margin: 0 0 0.5rem 0;">🎯 Great News!</h4>
        <p style="margin: 0;">We found <strong>12 potential matches</strong> who want to learn {user_data.get('skill_have', 'your skill')}
        and can teach {user_data.get('skill_want', 'what you want to learn')}.</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2 = st.columns([1, 1])

    with col1:
        st.markdown("### Your Profile Summary")
        st.markdown(f"""
        **Can Teach:** {user_data.get('skill_have', 'N/A')}
        **Experience:** {user_data.get('skill_level_teach', 'N/A')}
        **Wants to Learn:** {user_data.get('skill_want', 'N/A')}
        **Availability:** {user_data.get('availability', 'N/A')}
        **Commitment:** {user_data.get('commitment', 'N/A')} per week
        """)

    with col2:
        st.markdown("### What You Get")
        st.markdown("""
        ✅ Access to all 12 potential matches
        ✅ Direct contact information
        ✅ Conversation starters
        ✅ Priority in future matches
        ✅ Community support
        ✅ Progress tracking
        """)

    # Payment form
    with st.form("payment_form"):
        st.markdown("### Complete Your Membership")

        col1, col2 = st.columns(2)

        with col1:
            payment_name = st.text_input(
                "Full Name for Payment",
                value=user_data.get('name', ''),
                help="As it appears on your payment method"
            )
            payment_email = st.text_input(
                "Email for Receipt",
                value=user_data.get('email', ''),
                help="We'll send your receipt here"
            )

        with col2:
            st.markdown("#### Secure Payment")
            st.info("🔒 We use Razorpay for secure payments  \n💳 Your data is encrypted and safe  \n🔄 Cancel anytime, no hidden fees")

        # Pricing
        st.markdown(f"""
        <div style="text-align: center; background: {COLORS['neutral_50']}; padding: 1.5rem; border-radius: 8px; margin: 1rem 0;">
            <div style="font-size: 2.5rem; font-weight: 700; color: {COLORS['primary']};">₹49</div>
            <p style="color: {COLORS['neutral_500']}; margin: 0;">per month • Cancel anytime</p>
        </div>
        """, unsafe_allow_html=True)

        terms_payment = st.checkbox(
            "I agree to the payment terms and monthly subscription",
            help="You can cancel anytime from your dashboard"
        )

        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            payment_submitted = st.form_submit_button(
                "Start Learning - ₹49/month",
                type="primary",
                use_container_width=True
            )

        if payment_submitted:
            if not all([payment_name, payment_email, terms_payment]):
                st.markdown('<div class="error-message">Please fill all fields and agree to terms</div>', unsafe_allow_html=True)
            else:
                st.markdown('<div class="success-message">🎉 Payment successful! Welcome to SkillSwap Premium!</div>', unsafe_allow_html=True)
                st.session_state.authenticated = True
                st.session_state.user_email = payment_email
                time.sleep(2)
                navigate_to("dashboard")

# Dashboard for existing users
def show_dashboard():
    user_email = st.session_state.user_email
    user_data = st.session_state.user_data

    # Header
    st.markdown(f"""
    <div style="background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
         color: white; padding: 2rem; border-radius: 16px; margin-bottom: 2rem;">
        <h1 style="margin: 0 0 0.5rem 0;">Welcome back!</h1>
        <p style="margin: 0; opacity: 0.9;">Ready to connect with your learning partners?</p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Available Matches", "12", "3 new")
    with col2:
        st.metric("Active Conversations", "3", "1 new")
    with col3:
        st.metric("Skills Learned", "2", "+1")
    with col4:
        st.metric("Success Rate", "95%", "+5%")

    # Main content with tabs
    tab1, tab2, tab3 = st.tabs(["🎯 Your Matches", "💬 Messages", "👤 Profile"])

    with tab1:
        show_matches_tab()

    with tab2:
        show_messages_tab()

    with tab3:
        show_profile_tab()

    # Logout button
    if st.button("🚪 Logout"):
        st.session_state.authenticated = False
        st.session_state.user_email = None
        st.session_state.user_data = {}
        navigate_to("homepage")

# Matches tab content
def show_matches_tab():
    st.markdown("### People Who Want to Learn Your Skill")

    # Sample matches
    matches_want_to_learn = [
        {
            "name": "Arjun Patel",
            "skill_want": "React Development",
            "skill_have": "UI/UX Design",
            "experience": "3 years",
            "location": "Mumbai",
            "availability": "Weekday Evenings"
        },
        {
            "name": "Priya Sharma",
            "skill_want": "React Development",
            "skill_have": "Content Writing",
            "experience": "4 years",
            "location": "Delhi",
            "availability": "Flexible"
        }
    ]

    for match in matches_want_to_learn:
        with st.expander(f"👤 {match['name']} - {match['location']}"):
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                **Wants to Learn:** {match['skill_want']}
                **Can Teach:** {match['skill_have']}
                **Experience:** {match['experience']}
                """)
            with col2:
                st.markdown(f"""
                **Location:** {match['location']}
                **Availability:** {match['availability']}
                """)

            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button(f"💬 Message {match['name'].split()[0]}", key=f"msg_{match['name']}"):
                    st.success(f"Message sent to {match['name']}!")
            with col2:
                if st.button(f"👀 View Profile", key=f"profile_{match['name']}"):
                    st.info("Profile view feature coming soon!")
            with col3:
                if st.button(f"⭐ Save Match", key=f"save_{match['name']}"):
                    st.success("Match saved!")

    st.markdown("---")
    st.markdown("### People You Can Learn From")

    # Sample teachers
    matches_can_teach = [
        {
            "name": "Sneha Gupta",
            "skill_have": "UI/UX Design",
            "skill_want": "Digital Marketing",
            "experience": "5 years",
            "location": "Bangalore",
            "availability": "Weekends"
        }
    ]

    for match in matches_can_teach:
        with st.expander(f"👤 {match['name']} - {match['location']}"):
            col1, col2 = st.columns(2)
            with col1:
                st.markdown(f"""
                **Can Teach:** {match['skill_have']}
                **Wants to Learn:** {match['skill_want']}
                **Experience:** {match['experience']}
                """)
            with col2:
                st.markdown(f"""
                **Location:** {match['location']}
                **Availability:** {match['availability']}
                """)

            col1, col2, col3 = st.columns(3)
            with col1:
                if st.button(f"💬 Message {match['name'].split()[0]}", key=f"msg_teach_{match['name']}"):
                    st.success(f"Message sent to {match['name']}!")
            with col2:
                if st.button(f"👀 View Profile", key=f"profile_teach_{match['name']}"):
                    st.info("Profile view feature coming soon!")
            with col3:
                if st.button(f"⭐ Save Match", key=f"save_teach_{match['name']}"):
                    st.success("Match saved!")

# Messages tab content
def show_messages_tab():
    st.markdown("### Your Conversations")

    # Sample conversations
    conversations = [
        {
            "name": "Arjun Patel",
            "last_message": "Hi! I'd love to learn React from you. When can we start?",
            "time": "2 hours ago",
            "unread": True
        },
        {
            "name": "Priya Sharma",
            "last_message": "Thanks for the great session yesterday!",
            "time": "1 day ago",
            "unread": False
        }
    ]

    for conv in conversations:
        status = "🔴" if conv['unread'] else "⚪"
        with st.expander(f"{status} {conv['name']} - {conv['time']}"):
            st.markdown(f"**Last message:** {conv['last_message']}")
            if st.button(f"Reply to {conv['name']}", key=f"reply_{conv['name']}"):
                st.info("Messaging feature coming soon!")

# Profile tab content
def show_profile_tab():
    st.markdown("### Your Profile")

    if st.session_state.user_data:
        user_data = st.session_state.user_data

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("#### Personal Information")
            st.markdown(f"""
            **Name:** {user_data.get('name', 'N/A')}
            **Email:** {user_data.get('email', 'N/A')}
            **Location:** {user_data.get('location', 'Not specified')}
            **Experience:** {user_data.get('experience', 'N/A')}
            """)

        with col2:
            st.markdown("#### Skills & Availability")
            st.markdown(f"""
            **Can Teach:** {user_data.get('skill_have', 'N/A')}
            **Wants to Learn:** {user_data.get('skill_want', 'N/A')}
            **Availability:** {user_data.get('availability', 'N/A')}
            **Commitment:** {user_data.get('commitment', 'N/A')}
            """)

        if user_data.get('learning_goal'):
            st.markdown("#### Learning Goals")
            st.markdown(user_data['learning_goal'])

        if st.button("✏️ Edit Profile"):
            st.info("Profile editing feature coming soon!")
    else:
        st.info("No profile data available. Please complete your onboarding.")

if __name__ == "__main__":
    init_session_state()
    load_css()

    # Page routing
    if st.session_state.page == "homepage":
        show_homepage()
    elif st.session_state.page == "onboarding":
        show_onboarding()
    elif st.session_state.page == "login":
        show_login()
    elif st.session_state.page == "payment":
        show_payment()
    elif st.session_state.page == "dashboard":
        show_dashboard()
