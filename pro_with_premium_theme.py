import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime
import pandas as pd

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

def load_premium_theme():
    """Load the beautiful premium theme for SkillSwap"""
    st.markdown("""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap');
    
    /* Global Styles */
    .main {
        font-family: 'Inter', sans-serif;
        background: #F9FAFB;
        color: #1F2937;
        padding: 0;
    }
    
    /* Hide Streamlit elements */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}
    .stDeployButton {visibility: hidden;}
    
    /* Premium Header with Gradient */
    .premium-header {
        background: linear-gradient(90deg, #2F80ED, #27AE60);
        padding: 4rem 2rem;
        color: white;
        text-align: center;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }
    
    /* Abstract background shapes */
    .premium-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -20%;
        width: 300px;
        height: 300px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        transform: rotate(45deg);
    }
    
    .premium-header::after {
        content: '';
        position: absolute;
        bottom: -30%;
        left: -10%;
        width: 200px;
        height: 200px;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 50%;
    }
    
    .premium-header h1 {
        font-family: 'Poppins', sans-serif;
        font-size: 3.5rem;
        font-weight: 700;
        margin: 0 0 1rem 0;
        position: relative;
        z-index: 2;
    }
    
    .premium-header p {
        font-size: 1.3rem;
        opacity: 0.95;
        margin: 0;
        position: relative;
        z-index: 2;
    }
    
    /* Main Container */
    .main-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 64px;
    }
    
    /* Premium Cards */
    .premium-card {
        background: #FFFFFF;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        padding: 32px;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .premium-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-2px);
    }
    
    /* Typography */
    .heading-primary {
        font-family: 'Poppins', sans-serif;
        font-size: 2.5rem;
        font-weight: 700;
        color: #1F2937;
        margin-bottom: 1rem;
        text-align: center;
    }
    
    .heading-secondary {
        font-family: 'Poppins', sans-serif;
        font-size: 2rem;
        font-weight: 600;
        color: #1F2937;
        margin-bottom: 1rem;
    }
    
    .body-text {
        font-family: 'Inter', sans-serif;
        font-size: 16px;
        color: #6B7280;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }
    
    .body-text-large {
        font-family: 'Inter', sans-serif;
        font-size: 18px;
        color: #6B7280;
        line-height: 1.6;
        margin-bottom: 1.5rem;
        text-align: center;
    }
    
    /* Premium Buttons */
    .stButton > button {
        background: #2F80ED !important;
        color: white !important;
        padding: 16px 32px !important;
        border: none !important;
        border-radius: 12px !important;
        font-family: 'Inter', sans-serif !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        box-shadow: 0 2px 8px rgba(47, 128, 237, 0.3) !important;
        width: 100% !important;
        height: auto !important;
        min-height: 56px !important;
    }
    
    .stButton > button:hover {
        background: #2368C4 !important;
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.4) !important;
        transform: translateY(-2px) !important;
    }
    
    /* Trust Badges */
    .trust-badges {
        display: flex;
        justify-content: center;
        gap: 1.5rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }
    
    .trust-badge {
        background: linear-gradient(135deg, #2F80ED, #27AE60);
        color: white;
        padding: 12px 24px;
        border-radius: 25px;
        font-size: 14px;
        font-weight: 600;
        box-shadow: 0 4px 12px rgba(47, 128, 237, 0.2);
        transition: all 0.3s ease;
    }
    
    .trust-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 16px rgba(47, 128, 237, 0.3);
    }
    
    /* Statistics Grid */
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
        margin: 3rem 0;
    }
    
    .stat-card {
        background: #FFFFFF;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        transform: translateY(-4px);
    }
    
    .stat-number {
        font-family: 'Poppins', sans-serif;
        font-size: 3rem;
        font-weight: 700;
        color: #2F80ED;
        display: block;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-family: 'Inter', sans-serif;
        color: #6B7280;
        font-size: 16px;
        font-weight: 500;
    }
    
    /* Mobile Responsive */
    @media (max-width: 768px) {
        .main-container {
            padding: 0 16px;
        }
        
        .premium-header {
            padding: 3rem 1rem;
        }
        
        .premium-header h1 {
            font-size: 2.5rem;
        }
        
        .premium-card {
            padding: 24px;
        }
        
        .heading-primary {
            font-size: 2rem;
        }
        
        .heading-secondary {
            font-size: 1.5rem;
        }
        
        .trust-badges {
            gap: 1rem;
        }
        
        .trust-badge {
            font-size: 12px;
            padding: 10px 20px;
        }
        
        .stats-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }
        
        .stat-number {
            font-size: 2rem;
        }
        
        .stButton > button {
            font-size: 14px !important;
            padding: 14px 24px !important;
        }
    }
    </style>
    """, unsafe_allow_html=True)

# Setup Google Sheets (your existing code)
scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
creds = ServiceAccountCredentials.from_json_keyfile_dict(
    st.secrets["gcp_service_account"], scope
)
client = gspread.authorize(creds)
sheet = client.open("SkillSwap_Users").sheet1

# Initialize session state
if "submitted" not in st.session_state:
    st.session_state.submitted = False
if "page" not in st.session_state:
    st.session_state.page = "landing"

def create_header():
    """Create the premium header with gradient background"""
    st.markdown("""
    <div class="premium-header">
        <h1>SkillSwap</h1>
        <p>Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)

def create_hero_section():
    """Create the main hero section"""
    st.markdown("""
    <div class="main-container">
        <div class="premium-card">
            <h2 class="heading-primary">Exchange Skills. Build Your Career. Grow Your Network.</h2>
            <p class="body-text-large">
                Join 3,200+ professionals who are teaching what they know and learning what they need. 
                Quality matches, verified members, guaranteed results.
            </p>
            
            <div class="trust-badges">
                <div class="trust-badge">✓ Verified Community</div>
                <div class="trust-badge">✓ Perfect Matches</div>
                <div class="trust-badge">✓ Direct Contact</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def create_cta_section():
    """Create the call-to-action section"""
    st.markdown('<div class="main-container">', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown("""
        <div class="premium-card" style="text-align: center;">
            <h3 class="heading-secondary">Choose Your Path</h3>
            <p class="body-text">Join thousands of professionals exchanging skills and growing together</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Side-by-side buttons
        btn_col1, btn_col2 = st.columns(2)
        
        with btn_col1:
            if st.button("🚀 Start Your Journey", key="start_journey", help="New to SkillSwap? Sign up here!"):
                st.session_state.page = "register"
                st.rerun()
        
        with btn_col2:
            if st.button("👤 Login", key="login", help="Already have an account? Login here!"):
                st.session_state.page = "login"
                st.rerun()
    
    st.markdown('</div>', unsafe_allow_html=True)

def create_stats_section():
    """Create the statistics section"""
    st.markdown("""
    <div class="main-container">
        <div class="stats-grid">
            <div class="stat-card">
                <span class="stat-number">3,247</span>
                <div class="stat-label">Active Learners</div>
            </div>
            <div class="stat-card">
                <span class="stat-number" style="color: #27AE60;">1,856</span>
                <div class="stat-label">Skills Exchanged</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">4.9</span>
                <div class="stat-label">Average Rating</div>
            </div>
            <div class="stat-card">
                <span class="stat-number">92%</span>
                <div class="stat-label">Success Rate</div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)

def show_landing_page():
    """Show the premium landing page"""
    create_header()
    create_hero_section()
    create_cta_section()
    create_stats_section()

def show_registration_page():
    """Show registration page with premium styling"""
    if st.button("← Back to Home", key="back_home"):
        st.session_state.page = "landing"
        st.rerun()
    
    st.markdown("""
    <div class="main-container">
        <div class="premium-card">
            <h2 class="heading-primary">Join SkillSwap Community</h2>
            <p class="body-text-large">Tell us about your skills and start learning</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    with st.form("skill_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            name = st.text_input("Your Name", placeholder="e.g., Priya Sharma")
            email = st.text_input("Email Address", placeholder="<EMAIL>")
            skill_have = st.text_input("Skill you can teach", placeholder="e.g., React Development")
        
        with col2:
            skill_want = st.text_input("Skill you want to learn", placeholder="e.g., UI/UX Design")
            availability = st.selectbox("Availability", 
                                      ["Weekends Only", "Weekday Evenings", "Flexible Schedule"])
            commitment = st.selectbox("Time Commitment", 
                                    ["1-2 hours/week", "3-5 hours/week", "6+ hours/week"])
        
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            submitted = st.form_submit_button("Find My Matches", use_container_width=True)
        
        if submitted:
            if name and email and skill_have and skill_want:
                # Generate simple bio
                bio = f"Hi! I'm {name}. I can teach {skill_have} and want to learn {skill_want}."
                
                # Add to Google Sheets
                sheet.append_row([
                    datetime.now().isoformat(),
                    name, email, skill_have, skill_want, availability, bio, "No", "No"
                ])
                st.session_state.submitted = True
                st.session_state.page = "matches"
                st.rerun()
            else:
                st.error("Please fill in all fields")

def show_login_page():
    """Show login page with premium styling"""
    if st.button("← Back to Home", key="back_home_login"):
        st.session_state.page = "landing"
        st.rerun()
    
    st.markdown("""
    <div class="main-container">
        <div class="premium-card">
            <h2 class="heading-primary">Welcome Back!</h2>
            <p class="body-text-large">Enter your email to access your matches</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        with st.form("login_form"):
            email = st.text_input("Email Address", placeholder="Enter your registered email")
            submitted = st.form_submit_button("Access My Matches", use_container_width=True)
            
            if submitted:
                if email and "@" in email:
                    st.session_state.submitted = True
                    st.session_state.page = "matches"
                    st.rerun()
                else:
                    st.error("Please enter a valid email address.")

def show_matches_page():
    """Show matches page with premium styling"""
    st.markdown("""
    <div class="main-container">
        <div class="premium-card">
            <h2 class="heading-primary">🎉 Your Perfect Matches!</h2>
            <p class="body-text-large">Here are people who want to learn what you teach</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Get all data from sheet
    try:
        data = sheet.get_all_records()
        df = pd.DataFrame(data)
        
        if len(df) > 1:
            # Show other users
            for _, user in df.iloc[:-1].iterrows():
                st.markdown(f"""
                <div class="main-container">
                    <div class="premium-card">
                        <h3 style="color: #1F2937; margin-bottom: 1rem;">{user.get('Name', 'Unknown')}</h3>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                            <div>
                                <strong style="color: #27AE60;">Can Teach:</strong><br>
                                <span style="color: #6B7280;">{user.get('Skill_Have', 'Not specified')}</span>
                            </div>
                            <div>
                                <strong style="color: #2F80ED;">Wants to Learn:</strong><br>
                                <span style="color: #6B7280;">{user.get('Skill_Want', 'Not specified')}</span>
                            </div>
                        </div>
                        <p style="color: #6B7280; margin-top: 1rem;">Available: {user.get('Availability', 'Not specified')}</p>
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("You're the first user! Share this with friends to find matches.")
    except Exception as e:
        st.error(f"Error loading matches: {str(e)}")
    
    if st.button("← Back to Home", key="back_to_home"):
        st.session_state.page = "landing"
        st.rerun()

def main():
    """Main application function"""
    load_premium_theme()
    
    # Page routing
    if st.session_state.page == "landing":
        show_landing_page()
    elif st.session_state.page == "register":
        show_registration_page()
    elif st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "matches":
        show_matches_page()
    elif st.session_state.submitted:
        show_matches_page()

if __name__ == "__main__":
    main()
