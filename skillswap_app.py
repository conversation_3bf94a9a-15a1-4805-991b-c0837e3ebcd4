import streamlit as st

# Page configuration - MUST BE FIRST
st.set_page_config(
    page_title="SkillSwap - Exchange Skills, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="expanded"
)

import gspread
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from datetime import datetime, timedelta
import hashlib
import smtplib
import json
import time

# Try to import optional dependencies
try:
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    EMAIL_AVAILABLE = True
except ImportError:
    EMAIL_AVAILABLE = False

try:
    import razorpay
    RAZORPAY_AVAILABLE = True
except ImportError:
    RAZORPAY_AVAILABLE = False

try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False

# Initialize session state
def init_session_state():
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "user_email" not in st.session_state:
        st.session_state.user_email = None
    if "is_premium" not in st.session_state:
        st.session_state.is_premium = False
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}

# Google Sheets setup
@st.cache_resource
def setup_google_sheets():
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_dict(
        st.secrets["gcp_service_account"], scope
    )
    client = gspread.authorize(creds)
    
    # Setup sheets
    sheets = {
        'users': client.open("SkillSwap_Users").sheet1,
        'premium': client.open("SkillSwap_Premium").sheet1,
        'matches': client.open("SkillSwap_Matches").sheet1,
        'payments': client.open("SkillSwap_Payments").sheet1
    }
    return sheets

# OpenAI setup
@st.cache_resource
def setup_openai():
    if OPENAI_AVAILABLE:
        return OpenAI(api_key=st.secrets.get("OPENAI_API_KEY", ""))
    return None

# Razorpay setup
@st.cache_resource
def setup_razorpay():
    if RAZORPAY_AVAILABLE:
        return razorpay.Client(
            auth=(st.secrets.get("RAZORPAY_KEY_ID", ""), st.secrets.get("RAZORPAY_KEY_SECRET", ""))
        )
    return None

# Email setup
def send_email(to_email, subject, body):
    if not EMAIL_AVAILABLE:
        st.info(f"Email would be sent to {to_email}: {subject}")
        return True

    try:
        msg = MimeMultipart()
        msg['From'] = st.secrets.get("EMAIL", "")
        msg['To'] = to_email
        msg['Subject'] = subject

        msg.attach(MimeText(body, 'html'))

        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(st.secrets.get("EMAIL", ""), st.secrets.get("EMAIL_PASSWORD", ""))
        server.send_message(msg)
        server.quit()
        return True
    except Exception as e:
        st.error(f"Email sending failed: {str(e)}")
        return False

# User authentication functions
def hash_email(email):
    return hashlib.sha256(email.encode()).hexdigest()

def register_user(sheets, user_data):
    try:
        # Add to users sheet
        sheets['users'].append_row([
            datetime.now().isoformat(),
            user_data['name'],
            user_data['email'],
            user_data['skill_have'],
            user_data['skill_want'],
            user_data['availability'],
            user_data['bio'],
            hash_email(user_data['email']),
            "Free"
        ])
        return True
    except Exception as e:
        st.error(f"Registration failed: {str(e)}")
        return False

def check_premium_status(sheets, email):
    try:
        premium_data = sheets['premium'].get_all_records()
        premium_emails = [row['Email'] for row in premium_data if row.get('Status') == 'Active']
        return email in premium_emails
    except:
        return False

def get_user_data(sheets, email):
    try:
        users_data = sheets['users'].get_all_records()
        for user in users_data:
            if user.get('Email') == email:
                return user
        return None
    except:
        return None

# AI bio generation
def generate_bio(openai_client, skill_have, skill_want, name):
    if not OPENAI_AVAILABLE or not openai_client:
        return f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. Let's exchange skills!"

    try:
        prompt = f"Write a professional 2-sentence bio for {name} who can teach {skill_have} and wants to learn {skill_want}. Make it engaging and friendly."

        response = openai_client.completions.create(
            model="gpt-3.5-turbo-instruct",
            prompt=prompt,
            max_tokens=60,
            temperature=0.7
        )

        return response.choices[0].text.strip()
    except Exception as e:
        return f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. Let's exchange skills!"

# Payment processing
def create_payment_link(razorpay_client, user_email, user_name):
    if not RAZORPAY_AVAILABLE or not razorpay_client:
        st.info("Payment simulation: ₹49 payment link would be created for " + user_name)
        return "https://demo-payment-link.com"

    try:
        payment_link = razorpay_client.payment_link.create({
            "amount": 4900,  # ₹49 in paise
            "currency": "INR",
            "description": "SkillSwap Premium Subscription - Monthly",
            "customer": {
                "name": user_name,
                "email": user_email
            },
            "notify": {
                "sms": False,
                "email": True
            },
            "reminder_enable": True,
            "callback_url": "https://skillswap.streamlit.app/payment-success",
            "callback_method": "get"
        })
        return payment_link['short_url']
    except Exception as e:
        st.error(f"Payment link creation failed: {str(e)}")
        return None

# Matching algorithm
def find_matches(sheets, user_email, user_skill_have, user_skill_want):
    try:
        premium_users = sheets['premium'].get_all_records()
        active_premium_emails = [row['Email'] for row in premium_users if row.get('Status') == 'Active']
        
        users_data = sheets['users'].get_all_records()
        matches = []
        
        for user in users_data:
            if (user.get('Email') in active_premium_emails and 
                user.get('Email') != user_email):
                
                # Bidirectional matching
                if (user.get('Skill_Have', '').lower() == user_skill_want.lower() and
                    user.get('Skill_Want', '').lower() == user_skill_have.lower()):
                    matches.append(user)
        
        return matches
    except Exception as e:
        st.error(f"Matching failed: {str(e)}")
        return []

# Main application
def main():
    init_session_state()
    
    # Setup services
    sheets = setup_google_sheets()
    openai_client = setup_openai()
    razorpay_client = setup_razorpay()
    
    # Sidebar navigation
    st.sidebar.title("🔄 SkillSwap")
    
    if not st.session_state.authenticated:
        page = st.sidebar.selectbox("Navigate", ["Home", "Register", "Login"])
    else:
        if st.session_state.is_premium:
            page = st.sidebar.selectbox("Navigate", ["Dashboard", "My Matches", "Profile", "Analytics", "Logout"])
        else:
            page = st.sidebar.selectbox("Navigate", ["Dashboard", "Upgrade to Premium", "Profile", "Logout"])
    
    # Page routing
    if page == "Home":
        show_home_page()
    elif page == "Register":
        show_register_page(sheets, openai_client)
    elif page == "Login":
        show_login_page(sheets)
    elif page == "Dashboard":
        show_dashboard(sheets)
    elif page == "My Matches":
        show_matches_page(sheets)
    elif page == "Upgrade to Premium":
        show_premium_page(razorpay_client)
    elif page == "Profile":
        show_profile_page(sheets)
    elif page == "Analytics":
        show_analytics_page(sheets)
    elif page == "Logout":
        logout()

def show_home_page():
    st.title("🔄 SkillSwap")
    st.subheader("Exchange Skills, Grow Together")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        ### 🎯 How It Works
        1. **Register** with your skills
        2. **Upgrade** to Premium (₹49/month)
        3. **Get matched** with skill partners
        4. **Exchange** knowledge and grow!
        
        ### ✨ Why SkillSwap?
        - **Curated Community**: Only serious learners
        - **Perfect Matches**: AI-powered matching
        - **Quality Assured**: Premium membership ensures commitment
        """)
    
    with col2:
        st.markdown("""
        ### 💎 Premium Benefits
        - Access to all skill matches
        - Priority matching algorithm
        - Direct contact information
        - AI-generated introductions
        - Email notifications for new matches
        
        ### 🚀 Popular Skills
        - Web Development
        - Digital Marketing
        - Graphic Design
        - Data Science
        - Language Learning
        """)
    
    st.markdown("---")
    st.markdown("### 🎉 Ready to Start Learning?")
    
    col1, col2, col3 = st.columns(3)
    with col1:
        if st.button("🔥 Register Now", type="primary"):
            st.session_state.page = "Register"
            st.rerun()
    
    with col2:
        if st.button("🔑 Login"):
            st.session_state.page = "Login"
            st.rerun()
    
    with col3:
        st.markdown("**Only ₹49/month**")

def show_register_page(sheets, openai_client):
    st.title("🚀 Join SkillSwap")
    st.markdown("Create your account and start exchanging skills!")

    with st.form("registration_form"):
        col1, col2 = st.columns(2)

        with col1:
            name = st.text_input("Full Name*", placeholder="Enter your full name")
            email = st.text_input("Email Address*", placeholder="<EMAIL>")
            skill_have = st.text_input("Skill You Can Teach*", placeholder="e.g., Python Programming")

        with col2:
            skill_want = st.text_input("Skill You Want to Learn*", placeholder="e.g., Digital Marketing")
            availability = st.selectbox("Availability*", [
                "Weekends Only",
                "Weekday Evenings",
                "Flexible Schedule",
                "Weekday Mornings",
                "Custom Schedule"
            ])
            custom_availability = st.text_input("Custom Availability (if selected)", placeholder="Specify your availability")

        terms_agreed = st.checkbox("I agree to the Terms of Service and Privacy Policy*")
        submitted = st.form_submit_button("Create Account", type="primary")

        if submitted:
            if not all([name, email, skill_have, skill_want, availability]):
                st.error("Please fill in all required fields marked with *")
            elif not terms_agreed:
                st.error("Please agree to the Terms of Service")
            elif "@" not in email:
                st.error("Please enter a valid email address")
            else:
                # Check if user already exists
                existing_user = get_user_data(sheets, email)
                if existing_user:
                    st.error("An account with this email already exists. Please login instead.")
                else:
                    # Generate AI bio
                    with st.spinner("Generating your AI bio..."):
                        bio = generate_bio(openai_client, skill_have, skill_want, name)

                    # Prepare user data
                    final_availability = custom_availability if availability == "Custom Schedule" else availability
                    user_data = {
                        'name': name,
                        'email': email,
                        'skill_have': skill_have,
                        'skill_want': skill_want,
                        'availability': final_availability,
                        'bio': bio
                    }

                    # Register user
                    if register_user(sheets, user_data):
                        st.success("🎉 Account created successfully!")
                        st.info("Your AI-generated bio: " + bio)
                        st.markdown("### Next Steps:")
                        st.markdown("1. **Upgrade to Premium** to access matches")
                        st.markdown("2. **Get matched** with skill partners")
                        st.markdown("3. **Start learning** and teaching!")

                        # Auto-login
                        st.session_state.authenticated = True
                        st.session_state.user_email = email
                        st.session_state.user_data = user_data
                        st.session_state.is_premium = check_premium_status(sheets, email)

                        time.sleep(2)
                        st.rerun()

def show_login_page(sheets):
    st.title("🔑 Welcome Back")
    st.markdown("Login to your SkillSwap account")

    with st.form("login_form"):
        email = st.text_input("Email Address", placeholder="<EMAIL>")
        submitted = st.form_submit_button("Login", type="primary")

        if submitted:
            if not email:
                st.error("Please enter your email address")
            elif "@" not in email:
                st.error("Please enter a valid email address")
            else:
                user_data = get_user_data(sheets, email)
                if user_data:
                    st.session_state.authenticated = True
                    st.session_state.user_email = email
                    st.session_state.user_data = user_data
                    st.session_state.is_premium = check_premium_status(sheets, email)

                    st.success("Login successful!")
                    time.sleep(1)
                    st.rerun()
                else:
                    st.error("No account found with this email. Please register first.")

def show_dashboard(sheets):
    st.title(f"👋 Welcome back, {st.session_state.user_data.get('Name', 'User')}!")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Your Status", "Premium" if st.session_state.is_premium else "Free")

    with col2:
        if st.session_state.is_premium:
            matches = find_matches(sheets, st.session_state.user_email,
                                 st.session_state.user_data.get('Skill_Have', ''),
                                 st.session_state.user_data.get('Skill_Want', ''))
            st.metric("Available Matches", len(matches))
        else:
            st.metric("Available Matches", "Upgrade to Premium")

    with col3:
        st.metric("Skill Teaching", st.session_state.user_data.get('Skill_Have', 'N/A'))

    st.markdown("---")

    if st.session_state.is_premium:
        st.markdown("### 🎯 Your Recent Activity")
        st.info("✅ Premium member - Access all features!")

        if st.button("🔍 View My Matches", type="primary"):
            st.session_state.page = "My Matches"
            st.rerun()
    else:
        st.markdown("### 🚀 Upgrade to Premium")
        st.warning("Upgrade to Premium to access skill matches and connect with other learners!")

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("""
            **Premium Benefits:**
            - ✅ Access to all skill matches
            - ✅ Direct contact information
            - ✅ Priority matching
            - ✅ Email notifications
            """)

        with col2:
            if st.button("💎 Upgrade Now - ₹49/month", type="primary"):
                st.session_state.page = "Upgrade to Premium"
                st.rerun()

def show_matches_page(sheets):
    if not st.session_state.is_premium:
        st.error("Premium membership required to access matches!")
        return

    st.title("🎯 Your Skill Matches")
    st.markdown("Connect with people who want to learn what you teach, and can teach what you want to learn!")

    matches = find_matches(sheets, st.session_state.user_email,
                          st.session_state.user_data.get('Skill_Have', ''),
                          st.session_state.user_data.get('Skill_Want', ''))

    if matches:
        st.success(f"Found {len(matches)} perfect match(es) for you!")

        for i, match in enumerate(matches):
            with st.expander(f"🤝 Match {i+1}: {match.get('Name', 'Unknown')}"):
                col1, col2 = st.columns(2)

                with col1:
                    st.markdown(f"**Name:** {match.get('Name', 'N/A')}")
                    st.markdown(f"**Email:** {match.get('Email', 'N/A')}")
                    st.markdown(f"**Can Teach:** {match.get('Skill_Have', 'N/A')}")
                    st.markdown(f"**Wants to Learn:** {match.get('Skill_Want', 'N/A')}")

                with col2:
                    st.markdown(f"**Availability:** {match.get('Availability', 'N/A')}")
                    st.markdown(f"**Bio:** {match.get('Bio', 'No bio available')}")

                # Contact button
                if st.button(f"📧 Contact {match.get('Name', 'User')}", key=f"contact_{i}"):
                    # Send introduction email
                    subject = f"SkillSwap Match: {st.session_state.user_data.get('Name')} wants to connect!"
                    body = f"""
                    <h2>You have a new SkillSwap match!</h2>
                    <p><strong>{st.session_state.user_data.get('Name')}</strong> is interested in connecting with you for skill exchange.</p>

                    <h3>Their Details:</h3>
                    <ul>
                        <li><strong>Email:</strong> {st.session_state.user_email}</li>
                        <li><strong>Can Teach:</strong> {st.session_state.user_data.get('Skill_Have')}</li>
                        <li><strong>Wants to Learn:</strong> {st.session_state.user_data.get('Skill_Want')}</li>
                        <li><strong>Availability:</strong> {st.session_state.user_data.get('Availability')}</li>
                    </ul>

                    <p><strong>Bio:</strong> {st.session_state.user_data.get('Bio')}</p>

                    <p>Reply to this email to start your skill exchange journey!</p>
                    <p>Happy Learning!<br>The SkillSwap Team</p>
                    """

                    if send_email(match.get('Email'), subject, body):
                        st.success(f"Introduction email sent to {match.get('Name')}!")
                    else:
                        st.error("Failed to send email. Please try again.")
    else:
        st.info("No matches found yet. Don't worry, we'll notify you when someone with complementary skills joins!")
        st.markdown("### 💡 Tips to get more matches:")
        st.markdown("- Make sure your skills are clearly described")
        st.markdown("- Check back regularly as new users join daily")
        st.markdown("- Consider broadening your skill categories")

def show_premium_page(razorpay_client):
    st.title("💎 Upgrade to Premium")
    st.markdown("Unlock the full potential of SkillSwap!")

    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown("""
        ### 🚀 Premium Features

        ✅ **Access All Matches** - Connect with skill partners
        ✅ **Direct Contact Info** - Get email addresses instantly
        ✅ **Priority Matching** - Get matched first when new users join
        ✅ **Email Notifications** - Never miss a perfect match
        ✅ **AI-Powered Introductions** - Smart conversation starters
        ✅ **Advanced Analytics** - Track your learning progress

        ### 💰 Simple Pricing
        **₹49/month** - Cancel anytime, no hidden fees

        ### 🎯 Why Premium Works
        - **Quality Community**: Only serious learners invest in growth
        - **Better Matches**: Premium users are more committed
        - **Faster Results**: Skip the waiting, start learning today
        """)

    with col2:
        st.markdown("### 📊 Success Stats")
        st.metric("Premium Users", "500+")
        st.metric("Successful Matches", "1,200+")
        st.metric("Skills Exchanged", "50+")
        st.metric("Satisfaction Rate", "95%")

    st.markdown("---")

    # Payment form
    with st.form("payment_form"):
        st.markdown("### 💳 Complete Your Upgrade")

        col1, col2 = st.columns(2)
        with col1:
            payment_name = st.text_input("Full Name for Payment", value=st.session_state.user_data.get('Name', ''))
        with col2:
            payment_email = st.text_input("Email for Receipt", value=st.session_state.user_email)

        terms_payment = st.checkbox("I agree to the payment terms and monthly subscription")
        submitted = st.form_submit_button("🚀 Upgrade to Premium - ₹49/month", type="primary")

        if submitted:
            if not all([payment_name, payment_email, terms_payment]):
                st.error("Please fill all fields and agree to terms")
            else:
                with st.spinner("Creating payment link..."):
                    payment_url = create_payment_link(razorpay_client, payment_email, payment_name)

                if payment_url:
                    st.success("Payment link created successfully!")
                    st.markdown(f"[🔗 **Click here to complete payment**]({payment_url})")
                    st.info("After successful payment, refresh this page to access premium features.")
                else:
                    st.error("Failed to create payment link. Please try again.")

def show_profile_page(sheets):
    st.title("👤 Your Profile")

    user_data = st.session_state.user_data

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 📋 Current Information")
        st.markdown(f"**Name:** {user_data.get('Name', 'N/A')}")
        st.markdown(f"**Email:** {st.session_state.user_email}")
        st.markdown(f"**Status:** {'Premium' if st.session_state.is_premium else 'Free'}")
        st.markdown(f"**Can Teach:** {user_data.get('Skill_Have', 'N/A')}")
        st.markdown(f"**Wants to Learn:** {user_data.get('Skill_Want', 'N/A')}")
        st.markdown(f"**Availability:** {user_data.get('Availability', 'N/A')}")

    with col2:
        st.markdown("### 🤖 AI-Generated Bio")
        st.info(user_data.get('Bio', 'No bio available'))

    st.markdown("---")
    st.markdown("### ✏️ Update Profile")
    st.info("Profile updates coming soon! Contact support if you need to change your information.")

def show_analytics_page(sheets):
    if not st.session_state.is_premium:
        st.error("Premium membership required to access analytics!")
        return

    st.title("📊 SkillSwap Analytics")
    st.markdown("Insights into the SkillSwap community")

    try:
        # Get data
        users_data = sheets['users'].get_all_records()
        premium_data = sheets['premium'].get_all_records()

        # Basic metrics
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("Total Users", len(users_data))
        with col2:
            active_premium = len([u for u in premium_data if u.get('Status') == 'Active'])
            st.metric("Premium Users", active_premium)
        with col3:
            conversion_rate = (active_premium / len(users_data) * 100) if users_data else 0
            st.metric("Conversion Rate", f"{conversion_rate:.1f}%")
        with col4:
            st.metric("Total Revenue", f"₹{active_premium * 49:,}")

        # Popular skills
        st.markdown("### 🔥 Most Popular Skills to Learn")
        skills_want = [user.get('Skill_Want', '') for user in users_data if user.get('Skill_Want')]
        if skills_want:
            skill_counts = pd.Series(skills_want).value_counts().head(10)
            st.bar_chart(skill_counts)

        st.markdown("### 📚 Most Offered Skills")
        skills_have = [user.get('Skill_Have', '') for user in users_data if user.get('Skill_Have')]
        if skills_have:
            skill_counts = pd.Series(skills_have).value_counts().head(10)
            st.bar_chart(skill_counts)

    except Exception as e:
        st.error(f"Analytics loading failed: {str(e)}")

def logout():
    st.session_state.authenticated = False
    st.session_state.user_email = None
    st.session_state.is_premium = False
    st.session_state.user_data = {}
    st.success("Logged out successfully!")
    time.sleep(1)
    st.rerun()

if __name__ == "__main__":
    main()
