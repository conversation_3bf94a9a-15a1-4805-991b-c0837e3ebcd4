#!/bin/bash

# SkillSwap VPS Deployment Script
# Run this on Ubuntu 20.04+ or Debian 11+

echo "🚀 Starting SkillSwap VPS Deployment..."

# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and dependencies
sudo apt install -y python3 python3-pip python3-venv nginx certbot python3-certbot-nginx git curl

# Create application user
sudo useradd -m -s /bin/bash skillswap
sudo usermod -aG sudo skillswap

# Create application directory
sudo mkdir -p /var/www/skillswap
sudo chown skillswap:skillswap /var/www/skillswap

# Switch to application directory
cd /var/www/skillswap

# Create virtual environment
sudo -u skillswap python3 -m venv venv
sudo -u skillswap ./venv/bin/pip install --upgrade pip

# Copy application files (assuming they're in current directory)
sudo -u skillswap cp -r /home/<USER>/skillswap/* . 2>/dev/null || echo "Copy your SkillSwap files to /var/www/skillswap"

# Install Python dependencies
sudo -u skillswap ./venv/bin/pip install -r requirements.txt

# Create systemd service
sudo tee /etc/systemd/system/skillswap.service > /dev/null <<EOF
[Unit]
Description=SkillSwap Streamlit Application
After=network.target

[Service]
Type=simple
User=skillswap
WorkingDirectory=/var/www/skillswap
Environment=PATH=/var/www/skillswap/venv/bin
ExecStart=/var/www/skillswap/venv/bin/streamlit run skillswap_simple.py --server.port=8501 --server.address=127.0.0.1 --server.headless=true
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Create Nginx configuration
sudo tee /etc/nginx/sites-available/skillswap > /dev/null <<EOF
server {
    listen 80;
    server_name your-domain.com www.your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8501;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }

    # Handle Streamlit's WebSocket connections
    location /_stcore/stream {
        proxy_pass http://127.0.0.1:8501/_stcore/stream;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 86400;
    }
}
EOF

# Enable site
sudo ln -sf /etc/nginx/sites-available/skillswap /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t

# Start and enable services
sudo systemctl daemon-reload
sudo systemctl enable skillswap
sudo systemctl start skillswap
sudo systemctl enable nginx
sudo systemctl restart nginx

# Setup firewall
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw --force enable

echo "✅ SkillSwap deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Copy your SkillSwap files to /var/www/skillswap"
echo "2. Create /var/www/skillswap/.streamlit/secrets.toml with your API keys"
echo "3. Update server_name in /etc/nginx/sites-available/skillswap with your domain"
echo "4. Run: sudo systemctl restart skillswap"
echo "5. Setup SSL: sudo certbot --nginx -d your-domain.com"
echo ""
echo "🌐 Your app will be available at: http://your-domain.com"
echo "📊 Check status: sudo systemctl status skillswap"
echo "📝 View logs: sudo journalctl -u skillswap -f"
