# 🔄 SkillSwap - Revenue-Generating Skill Exchange Platform

A complete SaaS platform where users exchange skills for a monthly subscription fee. Built with Streamlit, Google Sheets, and integrated payment processing.

## 🚀 Features

### Core Platform
- **User Registration & Authentication** - Simple email-based login system
- **Skill Matching Algorithm** - Bidirectional matching (A teaches B, B teaches A)
- **Premium Subscription Model** - ₹49/month via Razorpay
- **AI-Generated Bios** - OpenAI-powered user introductions
- **Email Notifications** - Automated match notifications
- **Analytics Dashboard** - User growth and skill popularity insights

### Monetization
- **Freemium Model** - Free registration, paid access to matches
- **Razorpay Integration** - Secure payment processing
- **Subscription Management** - Monthly recurring billing
- **Premium Features** - Match access, contact info, priority matching

### Technical Stack
- **Frontend**: Streamlit (Python web framework)
- **Backend**: Google Sheets via gspread API
- **Payments**: Razorpay payment gateway
- **AI**: OpenAI GPT for bio generation
- **Email**: SMTP via Gmail
- **Deployment**: Streamlit Cloud

## 💰 Revenue Model

### Pricing Strategy
- **Free Tier**: Registration and profile creation
- **Premium Tier**: ₹49/month for match access and contact information

### Revenue Projections
| Month | Users | Premium | Monthly Revenue |
|-------|-------|---------|-----------------|
| 1     | 50    | 10      | ₹490           |
| 3     | 200   | 50      | ₹2,450         |
| 6     | 500   | 150     | ₹7,350         |
| 12    | 1000  | 300     | ₹14,700        |

## 🛠️ Setup Instructions

### 1. Prerequisites
- Python 3.8+
- Google Cloud account
- OpenAI account with API access
- Razorpay merchant account
- Gmail account with app password

### 2. Google Sheets Setup
Create 4 Google Sheets with these exact names:

1. **SkillSwap_Users**: `Timestamp | Name | Email | Skill_Have | Skill_Want | Availability | Bio | Hash | Status`
2. **SkillSwap_Premium**: `Timestamp | Name | Email | Payment_ID | Status | Expiry_Date`
3. **SkillSwap_Matches**: `Timestamp | User1_Email | User2_Email | Status | Contact_Made`
4. **SkillSwap_Payments**: `Timestamp | Email | Payment_ID | Amount | Status | Method`

### 3. API Configuration
1. **Google Cloud**: Enable Sheets & Drive APIs, create service account
2. **OpenAI**: Get API key, add billing credits
3. **Razorpay**: Complete KYC, get API keys
4. **Gmail**: Enable 2FA, generate app password

### 4. Local Development
```bash
# Clone repository
git clone <your-repo-url>
cd skillswap

# Install dependencies
pip install -r requirements.txt

# Configure secrets
cp secrets_template.toml .streamlit/secrets.toml
# Edit .streamlit/secrets.toml with your API keys

# Run locally
streamlit run skillswap_app.py
```

### 5. Deployment
1. Push to GitHub
2. Connect to Streamlit Cloud
3. Add secrets in Streamlit dashboard
4. Deploy with `skillswap_app.py` as main file

## 📊 File Structure

```
skillswap/
├── skillswap_app.py          # Main application
├── payment_handler.py        # Payment processing
├── requirements.txt          # Dependencies
├── secrets_template.toml     # Configuration template
├── SETUP_GUIDE.md           # Detailed setup instructions
└── README.md                # This file
```

## 🎯 Key Functions

### User Management
- `register_user()` - New user registration
- `check_premium_status()` - Verify subscription status
- `get_user_data()` - Retrieve user information

### Matching Algorithm
- `find_matches()` - Bidirectional skill matching
- Premium users only access
- Real-time match notifications

### Payment Processing
- `create_payment_link()` - Razorpay integration
- `handle_razorpay_webhook()` - Payment confirmations
- Automatic premium activation

### AI Features
- `generate_bio()` - OpenAI-powered user bios
- Skill-based personalization
- Fallback for API failures

## 🚀 Launch Strategy

### Phase 1: MVP Launch (Week 1-2)
- Deploy basic platform
- Test payment flow
- Onboard first 50 users

### Phase 2: Growth (Week 3-4)
- Social media marketing
- Referral program
- Content marketing

### Phase 3: Scale (Month 2+)
- Advanced features
- Corporate partnerships
- International expansion

## 📈 Growth Tactics

1. **Content Marketing**
   - Blog about skill exchange
   - LinkedIn articles
   - YouTube tutorials

2. **Community Building**
   - Facebook groups
   - Discord server
   - Reddit engagement

3. **Partnerships**
   - Educational institutions
   - Corporate training programs
   - Influencer collaborations

4. **SEO Optimization**
   - Skill-based landing pages
   - Local SEO for cities
   - Long-tail keywords

## 🔧 Customization

### Pricing Changes
Modify the payment amount in `skillswap_app.py`:
```python
"amount": 4900,  # ₹49 in paise
```

### Adding Features
- Video call integration (Zoom/Google Meet)
- Skill verification system
- Rating and reviews
- Group learning sessions
- Mobile app (React Native)

### Scaling Considerations
- Database migration (PostgreSQL)
- Caching layer (Redis)
- CDN for static assets
- Load balancing
- Monitoring and analytics

## 🛡️ Security & Compliance

- **Data Protection**: GDPR compliance ready
- **Payment Security**: PCI DSS compliant via Razorpay
- **Email Security**: App passwords, no plain text storage
- **API Security**: Rate limiting, input validation

## 📞 Support & Maintenance

### Monitoring
- Streamlit Cloud logs
- Payment gateway dashboards
- Google Sheets usage
- Email delivery rates

### Common Issues
- API rate limits
- Payment failures
- Email delivery problems
- Google Sheets permissions

## 🎉 Success Metrics

### Key Performance Indicators
- **User Acquisition**: New registrations per day
- **Conversion Rate**: Free to premium conversion
- **Retention Rate**: Monthly active premium users
- **Match Success**: Successful skill exchanges
- **Revenue Growth**: Monthly recurring revenue

### Analytics Dashboard
- User growth charts
- Popular skills analysis
- Revenue tracking
- Match success rates

## 📝 License

This project is proprietary software. All rights reserved.

## 🤝 Contributing

This is a commercial project. For feature requests or bug reports, please contact the development team.

---

**Ready to launch your skill exchange empire? Follow the setup guide and start generating revenue in 7-10 days!** 🚀
