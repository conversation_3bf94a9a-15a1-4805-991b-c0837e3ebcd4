OPENAI_API_KEY = "********************************************************************************************************************************************************************"
EMAIL = "<EMAIL>"
EMAIL_PASSWORD = "zawahfgoncrprvpz"

[gcp_service_account]
type = "service_account"
project_id = "avid-willow-453314-u5"
private_key_id = "f8c5fc622c37c24613b1429b13bfd2b11adf30c7"
private_key = "-----BEGIN PRIVATE KEY-----\\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC2lq4zPJo50Z/E\\nI54qS1h6gSqbs0M6t4K+edj3H68QrqXwaX3Ivp0rFr1RAjNGRrXy/R16ZLu/dZOc\\nSuwfFclkr1SHhLno/cs9oPdN+KzCTO10FwFDzJpJtYv5CWAQJk/oIyrO4jqAgr/c\\nl/9/iGdMO3aExGiq70U/wzifAu57qBFTSwaNAY8t6YKD/8WiVMKJ/oxa35rccNr6\\nY1eoOR7mOSMLVKwceM112vW1DnARojZPL38D8MgIloeVXVqGIsqR/Sn04wQxDHg8\\nblr8EwnII85DD2cxakqwQWXtEn9rp3t4MBITK3ARA3vV9YpJMrVvdmiyXaWoZJ7E\\nzt6Sue8pAgMBAAECggEAAidS1K3hT89GKCUXZ35OA9vkKcP6hgXGGAMN6RRJl1PI\\nB0bnQeflqK1y/DKfcjPBT6VmpJvtjvuPCyfjExbJslNTFuoc2z+rFwYABL5rIzuP\\nDXB8BlWyeC4ZAY33wRkClkVb6ubGM9/W7X4peFKejUqiQjyi02lREsh1sYJyV6kF\\nt/1wd6m4hkwScmns1RMJK8cAbYL7LQp4H7txh4qaTDJPdd4snXurqGfOcQ730B3r\\nXolXZ2WiG+2LJ8GQ43dRUf/1wdBob+40yJ+jFn9rKS/9ak03NwpGu6IwT0KoRPrk\\njR2oZGIXS7xh7iVjJP7RWt+iCQaMtN2Bip2giDxX0QKBgQDjW8ZBEt8cVIUW8PFc\\nlp8ak6mCyWs9AND4wUd6daRT9FxKEQyTUEqBEPGVkZIQAvPywtXuWcO1GeAzkpyH\\nTkoZwcpzBmRv6uLY8GI4KgPqn8l9f+E0sHYmj5eAI9kz8E6ope7KYI3BOjNkDc6x\\nVzS15t8/kgVve64JvcFTktXtXQKBgQDNlxf+Z9uDu7hK9UrvLDVoxO35K6XQxOQN\\nNZvxZ4aa/ZVtSTkIrvFQrJ+S2n2+FWSU3epPLaQhUYSfwEdmlNbGPBGtZv0Bc1Ie\\n207MVrGC+PHLiFpnaEiqnYsd7/uFOg0joC2Sh7vtKGOTzFoVbIpNIZkti3PJvgeV\\n+xMfvlvgPQKBgF53HVKgYq9Ett+nUMGlm4nRXJcI1VPNaQZSWD6bbqGrYEUt/Emh\\nDLO/tIDusP+SGoX9SiH0jgpLLAqBmiPl9YfqKxHL0CNoNsTobAbl/iaYKYHJhmr7\\n/rta4hnNy0t3R1dvSl4v6/YjFoM18XFhyHbj7FJfe36sps1JWHA/DD/JAoGABghP\\n8OwVOdlRQAnQiBIRAyD8JpqMRALy+hw/libGgVe9gbZrUQDpHcfi6sk0dVZlXCTi\\n0i3xqfSdYp6eubjWUHzEvATQmbFLkJI4zrAgh0pMtf8rP+uEblUvFtLK247ngcsa\\nazlGX2AXA+g0IHrN2BOLThXQcPP9fv4eAolwYrUCgYEAyOhG4WbG4ThqXgvlQ93g\\nHuUcwhfKpK0bov2jey3MFEN5427+WQwTrc8H4dmJi9c46Gj74uoo6wlvVeOSnHu7\\nuQPVhD+4J3EK1aobNMajn/F41evmLhirqTUuidbqbO1dKXEaBQKOMnftU23Klyqn\\nvU8piaCe7bXVLIblmV+6DJA=\\n-----END PRIVATE KEY-----\\n"
client_email = "<EMAIL>"
client_id = "115510758392249773655"
auth_uri = "https://accounts.google.com/o/oauth2/auth"
token_uri = "https://oauth2.googleapis.com/token"
auth_provider_x509_cert_url = "https://www.googleapis.com/oauth2/v1/certs"
client_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/skillswap-bot%40avid-willow-453314-u5.iam.gserviceaccount.com"
