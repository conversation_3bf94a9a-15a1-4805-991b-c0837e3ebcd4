import streamlit as st
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from datetime import datetime, timedelta
import razorpay
import hashlib
import hmac

# Payment success page
def payment_success_page():
    st.set_page_config(
        page_title="Payment Successful - SkillSwap",
        page_icon="✅",
        layout="centered"
    )
    
    st.title("🎉 Welcome to SkillSwap Premium!")
    st.success("Your payment was successful!")
    
    st.markdown("""
    ### What happens next?
    
    1. ✅ **Your account is now Premium**
    2. 🔍 **Access all skill matches**
    3. 📧 **Get email notifications for new matches**
    4. 🚀 **Start connecting with skill partners**
    
    ### Ready to find your matches?
    """)
    
    if st.button("🔄 Go to SkillSwap Dashboard", type="primary"):
        st.markdown("[Click here to access your dashboard](https://your-app-url.streamlit.app)")
    
    st.markdown("---")
    st.markdown("**Need help?** Contact <NAME_EMAIL>")

# Webhook handler for <PERSON><PERSON><PERSON><PERSON>
def handle_razorpay_webhook(webhook_data, webhook_signature):
    """
    Handle Razorpay webhook for payment confirmations
    This should be implemented as a separate endpoint
    """
    try:
        # Verify webhook signature
        webhook_secret = st.secrets["RAZORPAY_WEBHOOK_SECRET"]
        
        # Generate signature
        generated_signature = hmac.new(
            webhook_secret.encode('utf-8'),
            webhook_data.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
        
        if generated_signature != webhook_signature:
            return {"status": "error", "message": "Invalid signature"}
        
        # Parse webhook data
        import json
        data = json.loads(webhook_data)
        
        if data['event'] == 'payment_link.paid':
            # Payment successful
            payment_data = data['payload']['payment_link']['entity']
            customer_email = payment_data['customer']['email']
            payment_id = payment_data['id']
            amount = payment_data['amount']
            
            # Update premium status
            sheets = setup_google_sheets()
            
            # Add to premium sheet
            expiry_date = (datetime.now() + timedelta(days=30)).isoformat()
            sheets['premium'].append_row([
                datetime.now().isoformat(),
                payment_data['customer']['name'],
                customer_email,
                payment_id,
                "Active",
                expiry_date
            ])
            
            # Add to payments sheet
            sheets['payments'].append_row([
                datetime.now().isoformat(),
                customer_email,
                payment_id,
                amount / 100,  # Convert paise to rupees
                "Success",
                "Razorpay"
            ])
            
            # Send welcome email
            send_premium_welcome_email(customer_email)
            
            return {"status": "success", "message": "Payment processed"}
        
        return {"status": "ignored", "message": "Event not handled"}
        
    except Exception as e:
        return {"status": "error", "message": str(e)}

def send_premium_welcome_email(email):
    """Send welcome email to new premium users"""
    subject = "🎉 Welcome to SkillSwap Premium!"
    body = """
    <h2>Welcome to SkillSwap Premium!</h2>
    
    <p>Congratulations! Your payment was successful and you now have access to all premium features.</p>
    
    <h3>🚀 What you can do now:</h3>
    <ul>
        <li>✅ View all your skill matches</li>
        <li>📧 Get direct contact information</li>
        <li>🔔 Receive email notifications for new matches</li>
        <li>📊 Access advanced analytics</li>
    </ul>
    
    <h3>🎯 Next Steps:</h3>
    <ol>
        <li>Login to your SkillSwap account</li>
        <li>Check your "My Matches" section</li>
        <li>Start connecting with skill partners</li>
        <li>Begin your learning journey!</li>
    </ol>
    
    <p><a href="https://your-app-url.streamlit.app" style="background-color: #ff4b4b; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Access SkillSwap Dashboard</a></p>
    
    <p>Happy Learning!<br>
    The SkillSwap Team</p>
    
    <hr>
    <p><small>Your subscription will auto-renew monthly. You can cancel anytime from your account settings.</small></p>
    """
    
    # Use the same send_email function from main app
    return send_email(email, subject, body)

def setup_google_sheets():
    """Setup Google Sheets connection"""
    scope = ["https://spreadsheets.google.com/feeds", "https://www.googleapis.com/auth/drive"]
    creds = ServiceAccountCredentials.from_json_keyfile_dict(
        st.secrets["gcp_service_account"], scope
    )
    client = gspread.authorize(creds)
    
    sheets = {
        'users': client.open("SkillSwap_Users").sheet1,
        'premium': client.open("SkillSwap_Premium").sheet1,
        'matches': client.open("SkillSwap_Matches").sheet1,
        'payments': client.open("SkillSwap_Payments").sheet1
    }
    return sheets

def send_email(to_email, subject, body):
    """Send email using SMTP"""
    import smtplib
    from email.mime.text import MimeText
    from email.mime.multipart import MimeMultipart
    
    try:
        msg = MimeMultipart()
        msg['From'] = st.secrets["EMAIL"]
        msg['To'] = to_email
        msg['Subject'] = subject
        
        msg.attach(MimeText(body, 'html'))
        
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        server.login(st.secrets["EMAIL"], st.secrets["EMAIL_PASSWORD"])
        server.send_message(msg)
        server.quit()
        return True
    except Exception as e:
        print(f"Email sending failed: {str(e)}")
        return False

# Manual premium activation (for testing)
def manual_premium_activation():
    st.title("🔧 Manual Premium Activation")
    st.markdown("*For testing purposes only*")
    
    with st.form("manual_activation"):
        email = st.text_input("User Email")
        duration_days = st.number_input("Duration (days)", min_value=1, value=30)
        submitted = st.form_submit_button("Activate Premium")
        
        if submitted and email:
            try:
                sheets = setup_google_sheets()
                expiry_date = (datetime.now() + timedelta(days=duration_days)).isoformat()
                
                sheets['premium'].append_row([
                    datetime.now().isoformat(),
                    "Manual Activation",
                    email,
                    f"MANUAL_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    "Active",
                    expiry_date
                ])
                
                st.success(f"Premium activated for {email} for {duration_days} days!")
                
            except Exception as e:
                st.error(f"Activation failed: {str(e)}")

if __name__ == "__main__":
    # This can be used for testing payment success page
    payment_success_page()
