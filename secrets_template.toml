# Copy this file to .streamlit/secrets.toml and fill in your actual values

# OpenAI API Key
OPENAI_API_KEY = "your-openai-api-key-here"

# Email Configuration
EMAIL = "<EMAIL>"
EMAIL_PASSWORD = "your-gmail-app-password"

# Razorpay Configuration
RAZORPAY_KEY_ID = "your-razorpay-key-id"
RAZORPAY_KEY_SECRET = "your-razorpay-key-secret"

# Google Cloud Service Account (replace with your actual service account JSON)
[gcp_service_account]
type = "service_account"
project_id = "your-project-id"
private_key_id = "your-private-key-id"
private_key = """-----BEGIN PRIVATE KEY-----
your-private-key-content-here
-----END PRIVATE KEY-----"""
client_email = "<EMAIL>"
client_id = "your-client-id"
auth_uri = "https://accounts.google.com/o/oauth2/auth"
token_uri = "https://oauth2.googleapis.com/token"
auth_provider_x509_cert_url = "https://www.googleapis.com/oauth2/v1/certs"
client_x509_cert_url = "https://www.googleapis.com/robot/v1/metadata/x509/your-service-account%40your-project.iam.gserviceaccount.com"
universe_domain = "googleapis.com"
