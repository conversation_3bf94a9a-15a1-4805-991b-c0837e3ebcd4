# 🏠 SkillSwap Self-Hosting Guide

## 🎯 **Why Self-Host?**
- ✅ **Full Control** - Your domain, your branding
- ✅ **Better Performance** - Dedicated resources
- ✅ **No Platform Limits** - Unlimited users/requests
- ✅ **Professional Image** - Custom domain builds trust
- ✅ **Cost Effective** - $5-20/month vs platform fees

## 🚀 **Hosting Options (Ranked by Ease)**

### 1. **Docker Deployment (Easiest)**
**Best for**: Beginners, quick setup
**Cost**: $5-10/month
**Time**: 15 minutes

#### Platforms that support Docker:
- **DigitalOcean App Platform** (Recommended)
- **Railway.app**
- **Render.com**
- **Heroku**
- **AWS Lightsail**

#### Quick Docker Setup:
```bash
# 1. Build and run locally
docker build -t skillswap .
docker run -p 8501:8501 skillswap

# 2. Or use docker-compose
docker-compose up -d
```

### 2. **VPS Deployment (Most Popular)**
**Best for**: Full control, scalability
**Cost**: $5-20/month
**Time**: 30 minutes

#### Recommended VPS Providers:
- **DigitalOcean** - $5/month droplet
- **Linode** - $5/month nanode
- **Vultr** - $3.50/month instance
- **Hetzner** - €3.29/month server

#### VPS Setup Steps:
```bash
# 1. Create Ubuntu 20.04+ server
# 2. Run deployment script
chmod +x deploy_vps.sh
sudo ./deploy_vps.sh

# 3. Copy your files
scp -r . user@your-server:/var/www/skillswap/

# 4. Configure secrets
nano /var/www/skillswap/.streamlit/secrets.toml

# 5. Start the service
sudo systemctl restart skillswap
```

### 3. **Cloud Platform Deployment**
**Best for**: Enterprise, high traffic
**Cost**: $10-50/month
**Time**: 45 minutes

#### Cloud Options:
- **AWS EC2** + Elastic Load Balancer
- **Google Cloud Compute Engine**
- **Azure Virtual Machines**
- **Oracle Cloud (Free tier available)**

## 🔧 **Step-by-Step Setup**

### Option A: DigitalOcean Droplet (Recommended)

#### 1. Create Droplet
- Go to [DigitalOcean](https://digitalocean.com)
- Create $5/month Ubuntu 20.04 droplet
- Add your SSH key

#### 2. Connect and Setup
```bash
# Connect to server
ssh root@your-server-ip

# Run deployment script
wget https://your-repo/deploy_vps.sh
chmod +x deploy_vps.sh
./deploy_vps.sh
```

#### 3. Upload Your Files
```bash
# From your local machine
scp -r skillswap_simple.py requirements.txt root@your-server:/var/www/skillswap/
scp -r .streamlit root@your-server:/var/www/skillswap/
```

#### 4. Configure Domain
```bash
# Update Nginx config
nano /etc/nginx/sites-available/skillswap
# Change server_name to your domain

# Restart services
systemctl restart nginx
systemctl restart skillswap

# Setup SSL
certbot --nginx -d yourdomain.com
```

### Option B: Docker on Railway.app

#### 1. Setup Railway
- Go to [Railway.app](https://railway.app)
- Connect your GitHub repository
- Deploy from Dockerfile

#### 2. Environment Variables
Add these in Railway dashboard:
```
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0
```

#### 3. Custom Domain
- Add your domain in Railway settings
- Update DNS records as instructed

## 🌐 **Domain & SSL Setup**

### 1. **Buy Domain**
- **Namecheap** - $8-12/year
- **Cloudflare** - $8-10/year
- **GoDaddy** - $12-15/year

### 2. **DNS Configuration**
```
Type: A
Name: @
Value: your-server-ip

Type: A  
Name: www
Value: your-server-ip
```

### 3. **SSL Certificate**
```bash
# Free SSL with Let's Encrypt
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

## 📊 **Performance Optimization**

### 1. **Server Resources**
```bash
# Monitor usage
htop
df -h
free -m

# Optimize for Streamlit
echo 'vm.swappiness=10' >> /etc/sysctl.conf
```

### 2. **Nginx Optimization**
- Enable gzip compression ✅
- Set up caching headers ✅
- Configure rate limiting ✅
- Add security headers ✅

### 3. **Application Optimization**
```python
# Add to your app
@st.cache_data
def load_data():
    # Cache expensive operations
    pass

@st.cache_resource
def setup_connections():
    # Cache database connections
    pass
```

## 🔒 **Security Best Practices**

### 1. **Server Security**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Configure firewall
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# Disable root login
sudo nano /etc/ssh/sshd_config
# Set: PermitRootLogin no
```

### 2. **Application Security**
- Use environment variables for secrets
- Enable HTTPS only
- Set up rate limiting
- Regular backups

### 3. **Monitoring**
```bash
# Check app status
sudo systemctl status skillswap

# View logs
sudo journalctl -u skillswap -f

# Monitor resources
sudo apt install htop iotop
```

## 💰 **Cost Breakdown**

### Monthly Costs:
- **VPS**: $5-20/month
- **Domain**: $1/month (annual)
- **SSL**: Free (Let's Encrypt)
- **Monitoring**: Free (basic)
- **Total**: $6-21/month

### vs Streamlit Cloud:
- **Streamlit Cloud**: Free (limited) / $20+/month
- **Self-hosted**: $6-21/month (unlimited)
- **Savings**: $0-$50+/month

## 🚀 **Deployment Commands**

### Quick Deploy (Copy-Paste):
```bash
# 1. Create server and connect
ssh root@your-server-ip

# 2. Run deployment
curl -sSL https://your-repo/deploy_vps.sh | bash

# 3. Upload files (from local machine)
scp -r . root@your-server-ip:/var/www/skillswap/

# 4. Configure and start
ssh root@your-server-ip
cd /var/www/skillswap
nano .streamlit/secrets.toml  # Add your API keys
sudo systemctl restart skillswap

# 5. Setup domain
nano /etc/nginx/sites-available/skillswap  # Update server_name
sudo systemctl restart nginx
sudo certbot --nginx -d yourdomain.com
```

## 📈 **Scaling Options**

### When you grow:
1. **Upgrade server** - More CPU/RAM
2. **Load balancing** - Multiple servers
3. **Database** - Move from Google Sheets to PostgreSQL
4. **CDN** - Cloudflare for static assets
5. **Monitoring** - Grafana + Prometheus

## 🆘 **Troubleshooting**

### Common Issues:
```bash
# App not starting
sudo systemctl status skillswap
sudo journalctl -u skillswap -f

# Nginx errors
sudo nginx -t
sudo systemctl status nginx

# SSL issues
sudo certbot certificates
sudo certbot renew --dry-run

# Port conflicts
sudo netstat -tulpn | grep :8501
```

## 🎉 **Success Checklist**

- [ ] Server created and accessible
- [ ] Application deployed and running
- [ ] Domain configured and pointing to server
- [ ] SSL certificate installed
- [ ] All features tested
- [ ] Monitoring set up
- [ ] Backups configured

**Your SkillSwap platform is now self-hosted and ready to generate revenue!** 🚀

**Estimated setup time**: 30-60 minutes
**Monthly cost**: $6-21 (vs $20+ on platforms)
**Performance**: Better than shared platforms
**Control**: 100% yours!
