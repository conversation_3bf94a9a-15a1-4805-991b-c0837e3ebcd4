import streamlit as st
import pandas as pd
from datetime import datetime
import hashlib
import time

# Professional Design System
COLORS = {
    "primary": "#2563EB",        # Professional blue
    "secondary": "#059669",      # Success green
    "accent": "#F59E0B",         # Attention amber
    "neutral_50": "#F8FAFC",     # Light background
    "neutral_100": "#F1F5F9",    # Card background
    "neutral_500": "#64748B",    # Muted text
    "neutral_900": "#0F172A",    # Primary text
    "success": "#10B981",        # Success states
    "error": "#EF4444",          # Error states
    "white": "#FFFFFF"
}

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Enhanced CSS for Perfect Centering
def load_css():
    st.markdown(f"""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {{
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, {COLORS['neutral_50']} 0%, #E0E7FF 100%);
        padding: 0;
    }}
    
    /* Hide Streamlit elements */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    .stDeployButton {{visibility: hidden;}}
    
    /* Header */
    .header {{
        background: linear-gradient(135deg, {COLORS['primary']} 0%, {COLORS['secondary']} 100%);
        padding: 2rem;
        border-radius: 0 0 24px 24px;
        color: white;
        text-align: center;
        margin-bottom: 3rem;
        box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.2);
    }}
    
    .header h1 {{
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
    
    .header p {{
        font-size: 1.2rem;
        opacity: 0.95;
        margin: 0;
    }}
    
    /* Hero Section */
    .hero-container {{
        max-width: 900px;
        margin: 0 auto;
        text-align: center;
        padding: 2rem;
    }}
    
    .hero-title {{
        font-size: 3rem;
        font-weight: 700;
        color: {COLORS['neutral_900']};
        margin-bottom: 1.5rem;
        line-height: 1.2;
    }}
    
    .hero-subtitle {{
        font-size: 1.3rem;
        color: {COLORS['neutral_500']};
        margin-bottom: 2rem;
        line-height: 1.6;
        max-width: 700px;
        margin-left: auto;
        margin-right: auto;
    }}
    
    /* CTA Section - Perfectly Centered */
    .cta-section {{
        max-width: 450px;
        margin: 4rem auto;
        padding: 0 2rem;
    }}
    
    .cta-card {{
        background: linear-gradient(135deg, {COLORS['white']}, {COLORS['neutral_50']});
        border: 2px solid {COLORS['neutral_100']};
        border-radius: 24px;
        padding: 3rem 2.5rem;
        text-align: center;
        box-shadow: 0 12px 40px rgba(37, 99, 235, 0.12);
        transition: all 0.4s ease;
        position: relative;
        overflow: hidden;
    }}
    
    .cta-card::before {{
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, {COLORS['primary']}, {COLORS['secondary']});
    }}
    
    .cta-card:hover {{
        transform: translateY(-8px);
        box-shadow: 0 20px 50px rgba(37, 99, 235, 0.18);
    }}
    
    .cta-title {{
        font-size: 1.8rem;
        font-weight: 600;
        color: {COLORS['primary']};
        margin-bottom: 1rem;
    }}
    
    .cta-subtitle {{
        font-size: 1.1rem;
        color: {COLORS['neutral_500']};
        margin-bottom: 2.5rem;
        line-height: 1.5;
    }}
    
    /* Enhanced Button Styling */
    .stButton > button {{
        background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']}) !important;
        color: white !important;
        padding: 1.4rem 3rem !important;
        border: none !important;
        border-radius: 16px !important;
        font-weight: 600 !important;
        font-size: 1.3rem !important;
        cursor: pointer !important;
        transition: all 0.4s ease !important;
        box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3) !important;
        width: 100% !important;
        height: auto !important;
        min-height: 70px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        position: relative !important;
        overflow: hidden !important;
        margin-bottom: 1.5rem !important;
    }}
    
    .stButton > button:hover {{
        transform: translateY(-4px) !important;
        box-shadow: 0 16px 35px rgba(37, 99, 235, 0.4) !important;
        background: linear-gradient(135deg, {COLORS['secondary']}, {COLORS['primary']}) !important;
    }}
    
    .stButton > button:active {{
        transform: translateY(-2px) !important;
    }}
    
    /* Button Shine Effect */
    .stButton > button::before {{
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.6s;
    }}
    
    .stButton > button:hover::before {{
        left: 100%;
    }}
    
    /* Secondary Button (Login) */
    .login-button {{
        background: {COLORS['white']} !important;
        color: {COLORS['primary']} !important;
        border: 2px solid {COLORS['primary']} !important;
        box-shadow: 0 4px 15px rgba(37, 99, 235, 0.2) !important;
    }}
    
    .login-button:hover {{
        background: {COLORS['primary']} !important;
        color: white !important;
        box-shadow: 0 12px 30px rgba(37, 99, 235, 0.3) !important;
    }}
    
    /* Trust Badges */
    .trust-badges {{
        display: flex;
        justify-content: center;
        gap: 1rem;
        margin: 2rem 0;
        flex-wrap: wrap;
    }}
    
    .badge {{
        background: {COLORS['success']};
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
    }}
    
    /* Helper Text */
    .helper-text {{
        text-align: center;
        margin-top: 2rem;
        padding: 1.5rem;
        background: {COLORS['neutral_50']};
        border-radius: 12px;
        border: 1px solid {COLORS['neutral_100']};
    }}
    
    .helper-text p {{
        color: {COLORS['neutral_500']};
        font-size: 1rem;
        line-height: 1.6;
        margin: 0;
    }}
    
    .helper-text strong {{
        color: {COLORS['neutral_900']};
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .header {{
            padding: 1.5rem;
        }}
        
        .header h1 {{
            font-size: 2rem;
        }}
        
        .hero-title {{
            font-size: 2.2rem;
        }}
        
        .hero-subtitle {{
            font-size: 1.1rem;
        }}
        
        .cta-section {{
            margin: 2rem auto;
            padding: 0 1rem;
        }}
        
        .cta-card {{
            padding: 2rem 1.5rem;
        }}
        
        .cta-title {{
            font-size: 1.5rem;
        }}
        
        .stButton > button {{
            font-size: 1.1rem !important;
            padding: 1.2rem 2rem !important;
            min-height: 60px !important;
        }}
        
        .trust-badges {{
            gap: 0.5rem;
        }}
        
        .badge {{
            font-size: 0.8rem;
            padding: 0.4rem 0.8rem;
        }}
    }}
    </style>
    """, unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "home"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False

# Home Page with Perfectly Centered CTAs
def show_home_page():
    # Hero Section
    st.markdown(f"""
    <div class="hero-container">
        <h1 class="hero-title">
            Exchange Skills.<br>Build Your Career.<br>Grow Your Network.
        </h1>
        <p class="hero-subtitle">
            Join 3,200+ professionals who are teaching what they know and learning what they need. 
            Quality matches, verified members, guaranteed results.
        </p>
        
        <div class="trust-badges">
            <span class="badge">✓ Verified Community</span>
            <span class="badge">✓ Perfect Matches</span>
            <span class="badge">✓ Direct Contact</span>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Perfectly Centered CTA Section
    st.markdown(f"""
    <div class="cta-section">
        <div class="cta-card">
            <h2 class="cta-title">Choose Your Path</h2>
            <p class="cta-subtitle">Join thousands of professionals exchanging skills and growing together</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Centered buttons with proper spacing
    col1, col2, col3 = st.columns([0.5, 1, 0.5])
    
    with col2:
        # Primary CTA - Start Journey
        if st.button("🚀 Start Your Journey", key="start_journey", help="New to SkillSwap? Sign up and find your first match!", use_container_width=True):
            st.session_state.page = "register"
            st.rerun()
        
        # Secondary CTA - Login  
        if st.button("👤 Login", key="login", help="Already have an account? Access your matches now!", use_container_width=True):
            st.session_state.page = "login"
            st.rerun()
        
        # Helper text
        st.markdown(f"""
        <div class="helper-text">
            <p>
                <strong>New to SkillSwap?</strong> Start your journey to find learning partners<br>
                <strong>Already a member?</strong> Login to see your latest matches
            </p>
        </div>
        """, unsafe_allow_html=True)

# Simple registration page
def show_register_page():
    if st.button("← Back to Home", key="back_home"):
        st.session_state.page = "home"
        st.rerun()
    
    st.markdown('<h2 style="text-align: center; color: #2563EB;">Join SkillSwap Community</h2>', unsafe_allow_html=True)
    
    with st.form("registration_form"):
        name = st.text_input("Full Name", placeholder="e.g., Priya Sharma")
        email = st.text_input("Email Address", placeholder="<EMAIL>")
        skill_have = st.text_input("Skill You Can Teach", placeholder="e.g., React Development")
        skill_want = st.text_input("Skill You Want to Learn", placeholder="e.g., UI/UX Design")
        availability = st.selectbox("Availability", ["Weekends Only", "Weekday Evenings", "Flexible Schedule"])
        
        submitted = st.form_submit_button("Join SkillSwap", use_container_width=True)
        
        if submitted:
            if all([name, email, skill_have, skill_want]):
                st.success("🎉 Welcome to SkillSwap! Finding your matches...")
                st.session_state.authenticated = True
                time.sleep(2)
                st.session_state.page = "dashboard"
                st.rerun()
            else:
                st.error("Please fill in all fields.")

# Simple login page
def show_login_page():
    if st.button("← Back to Home", key="back_home_login"):
        st.session_state.page = "home"
        st.rerun()
    
    st.markdown('<h2 style="text-align: center; color: #2563EB;">Welcome Back!</h2>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        with st.form("login_form"):
            email = st.text_input("Email Address", placeholder="Enter your registered email")
            submitted = st.form_submit_button("Access My Matches", use_container_width=True)
            
            if submitted:
                if email and "@" in email:
                    st.success("Login successful! Redirecting...")
                    st.session_state.authenticated = True
                    time.sleep(1)
                    st.session_state.page = "dashboard"
                    st.rerun()
                else:
                    st.error("Please enter a valid email address.")

# Simple dashboard
def show_dashboard():
    st.markdown('<h2 style="text-align: center; color: #2563EB;">Your Dashboard</h2>', unsafe_allow_html=True)
    st.success("🎉 Welcome to your SkillSwap dashboard!")
    
    if st.button("🚪 Logout", key="logout"):
        st.session_state.authenticated = False
        st.session_state.page = "home"
        st.rerun()

# Main application
def main():
    init_session_state()
    load_css()
    
    # Header
    st.markdown(f"""
    <div class="header">
        <h1>SkillSwap</h1>
        <p>Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Page routing
    if st.session_state.page == "home":
        show_home_page()
    elif st.session_state.page == "register":
        show_register_page()
    elif st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "dashboard":
        show_dashboard()

if __name__ == "__main__":
    main()
