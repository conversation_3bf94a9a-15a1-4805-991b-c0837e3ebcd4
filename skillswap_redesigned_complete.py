import streamlit as st
import pandas as pd
from datetime import datetime
import hashlib
import time

# Professional Design System
COLORS = {
    "primary": "#2563EB",        # Professional blue
    "secondary": "#059669",      # Success green
    "accent": "#F59E0B",         # Attention amber
    "neutral_50": "#F8FAFC",     # Light background
    "neutral_100": "#F1F5F9",    # Card background
    "neutral_500": "#64748B",    # Muted text
    "neutral_900": "#0F172A",    # Primary text
    "success": "#10B981",        # Success states
    "error": "#EF4444",          # Error states
    "white": "#FFFFFF"
}

# Page configuration
st.set_page_config(
    page_title="SkillSwap - Learn Together, Grow Together",
    page_icon="🔄",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Professional CSS Styling
def load_css():
    st.markdown(f"""
    <style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Global Styles */
    .main {{
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, {COLORS['neutral_50']} 0%, #E0E7FF 100%);
        padding: 0;
    }}
    
    /* Hide Streamlit elements */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}
    .stDeployButton {{visibility: hidden;}}
    
    /* Header */
    .header {{
        background: linear-gradient(135deg, {COLORS['primary']} 0%, {COLORS['secondary']} 100%);
        padding: 2rem;
        border-radius: 0 0 24px 24px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
        box-shadow: 0 10px 25px -5px rgba(37, 99, 235, 0.2);
    }}
    
    .header h1 {{
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0 0 0.5rem 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }}
    
    .header p {{
        font-size: 1.2rem;
        opacity: 0.95;
        margin: 0;
    }}
    
    /* Cards */
    .card {{
        background: {COLORS['white']};
        padding: 2rem;
        border-radius: 16px;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        border: 1px solid {COLORS['neutral_100']};
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
    }}
    
    .card:hover {{
        transform: translateY(-2px);
        box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.15);
    }}
    
    /* Buttons */
    .btn-primary {{
        background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
        color: white;
        padding: 1rem 2rem;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.3);
        text-decoration: none;
        display: inline-block;
        text-align: center;
        width: 100%;
        margin-bottom: 1rem;
    }}
    
    .btn-primary:hover {{
        transform: translateY(-2px);
        box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.4);
    }}
    
    .btn-secondary {{
        background: {COLORS['white']};
        color: {COLORS['primary']};
        padding: 1rem 2rem;
        border: 2px solid {COLORS['primary']};
        border-radius: 12px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.2s ease;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        width: 100%;
        margin-bottom: 1rem;
    }}
    
    .btn-secondary:hover {{
        background: {COLORS['primary']};
        color: white;
        transform: translateY(-1px);
    }}
    
    /* Stats Grid */
    .stats-grid {{
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin: 2rem 0;
    }}
    
    .stat-card {{
        background: {COLORS['white']};
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: 1px solid {COLORS['neutral_100']};
    }}
    
    .stat-number {{
        font-size: 2.5rem;
        font-weight: 700;
        color: {COLORS['primary']};
        display: block;
        margin-bottom: 0.5rem;
    }}
    
    .stat-label {{
        color: {COLORS['neutral_500']};
        font-size: 0.9rem;
        font-weight: 500;
    }}
    
    /* Profile Cards */
    .profile-card {{
        background: {COLORS['white']};
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        border: 1px solid {COLORS['neutral_100']};
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }}
    
    .profile-card:hover {{
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        transform: translateY(-2px);
    }}
    
    .profile-avatar {{
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']});
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 600;
        font-size: 1.2rem;
        margin-bottom: 1rem;
    }}
    
    /* Badges */
    .badge {{
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        margin: 0.25rem;
    }}
    
    .badge-success {{
        background: {COLORS['success']};
        color: white;
    }}
    
    .badge-primary {{
        background: {COLORS['primary']};
        color: white;
    }}
    
    /* Form Styling */
    .stTextInput > div > div > input {{
        border-radius: 8px;
        border: 2px solid {COLORS['neutral_100']};
        padding: 0.75rem;
        font-size: 1rem;
        transition: border-color 0.2s ease;
    }}
    
    .stTextInput > div > div > input:focus {{
        border-color: {COLORS['primary']};
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }}
    
    .stSelectbox > div > div > select {{
        border-radius: 8px;
        border: 2px solid {COLORS['neutral_100']};
        padding: 0.75rem;
    }}
    
    /* Mobile Responsive */
    @media (max-width: 768px) {{
        .header {{
            padding: 1.5rem;
        }}
        
        .header h1 {{
            font-size: 2rem;
        }}
        
        .card {{
            padding: 1.5rem;
        }}
        
        .stats-grid {{
            grid-template-columns: repeat(2, 1fr);
            gap: 1rem;
        }}
    }}
    
    /* Success Messages */
    .success-message {{
        background: linear-gradient(135deg, {COLORS['success']}10, {COLORS['secondary']}10);
        border: 1px solid {COLORS['success']}30;
        border-radius: 8px;
        padding: 1rem;
        color: {COLORS['success']};
        font-weight: 500;
        margin: 1rem 0;
    }}
    
    /* Section Headers */
    .section-header {{
        font-size: 1.5rem;
        font-weight: 600;
        color: {COLORS['neutral_900']};
        margin: 2rem 0 1rem 0;
        text-align: center;
    }}
    
    .section-subtitle {{
        color: {COLORS['neutral_500']};
        text-align: center;
        margin-bottom: 2rem;
    }}
    </style>
    """, unsafe_allow_html=True)

# Initialize session state
def init_session_state():
    if "page" not in st.session_state:
        st.session_state.page = "home"
    if "user_data" not in st.session_state:
        st.session_state.user_data = {}
    if "authenticated" not in st.session_state:
        st.session_state.authenticated = False
    if "is_premium" not in st.session_state:
        st.session_state.is_premium = False

# Trust signals and social proof
def show_trust_signals():
    st.markdown(f"""
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">3,247</span>
            <div class="stat-label">Active Learners</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">1,856</span>
            <div class="stat-label">Skills Exchanged</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">4.9</span>
            <div class="stat-label">Average Rating</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">92%</span>
            <div class="stat-label">Success Rate</div>
        </div>
    </div>
    """, unsafe_allow_html=True)

# Main application
def main():
    init_session_state()
    load_css()
    
    # Header
    st.markdown(f"""
    <div class="header">
        <h1>SkillSwap</h1>
        <p>Learn Together, Grow Together</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Page routing
    if st.session_state.page == "home":
        show_home_page()
    elif st.session_state.page == "register":
        show_register_page()
    elif st.session_state.page == "login":
        show_login_page()
    elif st.session_state.page == "dashboard":
        show_dashboard()
    elif st.session_state.page == "matches":
        show_matches_page()
    elif st.session_state.page == "profile":
        show_profile_page()

# Home Page with Clear CTAs
def show_home_page():
    # Hero Section
    col1, col2 = st.columns([3, 2])

    with col1:
        st.markdown(f"""
        <div class="card">
            <h2 style="color: {COLORS['neutral_900']}; font-size: 2.2rem; margin-bottom: 1rem;">
                Exchange Skills.<br>Build Your Career.<br>Grow Your Network.
            </h2>
            <p style="font-size: 1.1rem; color: {COLORS['neutral_500']}; margin-bottom: 2rem; line-height: 1.6;">
                Join 3,200+ professionals who are teaching what they know and learning what they need.
                Quality matches, verified members, guaranteed results.
            </p>

            <div style="margin-bottom: 2rem;">
                <span class="badge badge-success">✓ Verified Community</span>
                <span class="badge badge-success">✓ Perfect Matches</span>
                <span class="badge badge-success">✓ Direct Contact</span>
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <h3 style="color: {COLORS['primary']}; margin-bottom: 2rem;">Get Started</h3>
        """, unsafe_allow_html=True)

        # Clear CTAs
        if st.button("🚀 Start Your Journey", key="start_journey", help="New to SkillSwap? Sign up here"):
            st.session_state.page = "register"
            st.rerun()

        if st.button("👤 Login", key="login", help="Already have an account? Login here"):
            st.session_state.page = "login"
            st.rerun()

        st.markdown("""
        </div>
        """, unsafe_allow_html=True)

    # Trust signals
    show_trust_signals()

    # How it works
    st.markdown('<h2 class="section-header">How SkillSwap Works</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Simple, effective skill exchange in 3 steps</p>', unsafe_allow_html=True)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="background: {COLORS['primary']}; color: white; width: 60px; height: 60px;
                 border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                 font-weight: bold; margin-bottom: 1rem; font-size: 1.5rem;">1</div>
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1rem;">Share Your Skills</h3>
            <p style="color: {COLORS['neutral_500']};">Tell us what you can teach and what you want to learn</p>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="background: {COLORS['secondary']}; color: white; width: 60px; height: 60px;
                 border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                 font-weight: bold; margin-bottom: 1rem; font-size: 1.5rem;">2</div>
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1rem;">Get Matched</h3>
            <p style="color: {COLORS['neutral_500']};">Our algorithm finds perfect learning partners for you</p>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div style="background: {COLORS['accent']}; color: white; width: 60px; height: 60px;
                 border-radius: 50%; display: inline-flex; align-items: center; justify-content: center;
                 font-weight: bold; margin-bottom: 1rem; font-size: 1.5rem;">3</div>
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1rem;">Start Learning</h3>
            <p style="color: {COLORS['neutral_500']};">Connect directly and begin your skill exchange journey</p>
        </div>
        """, unsafe_allow_html=True)

    # Testimonials
    show_testimonials()

    # Pricing
    show_pricing_section()

# Testimonials Section
def show_testimonials():
    st.markdown('<h2 class="section-header">What Our Community Says</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Real stories from successful skill exchanges</p>', unsafe_allow_html=True)

    testimonials = [
        {
            "text": "I learned React from Priya and taught her digital marketing. We both landed better jobs within 3 months!",
            "author": "Rahul Kumar",
            "role": "Software Developer",
            "avatar": "RK",
            "rating": 5
        },
        {
            "text": "The community here is incredible. I've completed 3 skill exchanges and gained valuable connections.",
            "author": "Sneha Patel",
            "role": "UI/UX Designer",
            "avatar": "SP",
            "rating": 5
        },
        {
            "text": "Worth every rupee. The quality of matches is outstanding and the platform is easy to use.",
            "author": "Arjun Singh",
            "role": "Data Analyst",
            "avatar": "AS",
            "rating": 5
        }
    ]

    cols = st.columns(3)
    for i, testimonial in enumerate(testimonials):
        with cols[i]:
            st.markdown(f"""
            <div class="card">
                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                    <div class="profile-avatar" style="width: 50px; height: 50px; margin-right: 1rem; margin-bottom: 0;">
                        {testimonial['avatar']}
                    </div>
                    <div>
                        <div style="font-weight: 600; color: {COLORS['neutral_900']};">{testimonial['author']}</div>
                        <div style="color: {COLORS['neutral_500']}; font-size: 0.9rem;">{testimonial['role']}</div>
                        <div style="color: {COLORS['accent']};">{'★' * testimonial['rating']}</div>
                    </div>
                </div>
                <p style="font-style: italic; color: {COLORS['neutral_900']}; line-height: 1.6;">
                    "{testimonial['text']}"
                </p>
            </div>
            """, unsafe_allow_html=True)

# Pricing Section
def show_pricing_section():
    st.markdown('<h2 class="section-header">Simple, Transparent Pricing</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Get unlimited access to our learning community</p>', unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(f"""
        <div class="card" style="text-align: center; background: linear-gradient(135deg, {COLORS['primary']}10, {COLORS['secondary']}10);">
            <h3 style="color: {COLORS['primary']}; margin-bottom: 1rem;">Premium Access</h3>
            <div style="font-size: 3rem; font-weight: 700; color: {COLORS['primary']}; margin: 1rem 0;">₹49</div>
            <p style="color: {COLORS['neutral_500']}; margin-bottom: 2rem;">per month • Cancel anytime</p>

            <div style="text-align: left; max-width: 300px; margin: 0 auto 2rem auto;">
                <p style="margin: 0.5rem 0; display: flex; align-items: center;">
                    <span style="color: {COLORS['success']}; margin-right: 0.5rem;">✓</span>
                    Unlimited skill matches
                </p>
                <p style="margin: 0.5rem 0; display: flex; align-items: center;">
                    <span style="color: {COLORS['success']}; margin-right: 0.5rem;">✓</span>
                    Direct contact information
                </p>
                <p style="margin: 0.5rem 0; display: flex; align-items: center;">
                    <span style="color: {COLORS['success']}; margin-right: 0.5rem;">✓</span>
                    Priority matching algorithm
                </p>
                <p style="margin: 0.5rem 0; display: flex; align-items: center;">
                    <span style="color: {COLORS['success']}; margin-right: 0.5rem;">✓</span>
                    Community support
                </p>
                <p style="margin: 0.5rem 0; display: flex; align-items: center;">
                    <span style="color: {COLORS['success']}; margin-right: 0.5rem;">✓</span>
                    Progress tracking
                </p>
            </div>

            <button class="btn-primary" onclick="window.location.href='#'">
                Get Started Today
            </button>
        </div>
        """, unsafe_allow_html=True)

# Registration Page with Enhanced Form
def show_register_page():
    # Back button
    if st.button("← Back to Home", key="back_home"):
        st.session_state.page = "home"
        st.rerun()

    st.markdown('<h2 class="section-header">Join the SkillSwap Community</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Tell us about your skills and start learning</p>', unsafe_allow_html=True)

    # Progress indicator
    st.markdown(f"""
    <div class="card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
            <span style="font-weight: 600; color: {COLORS['neutral_900']};">Step 1 of 2: Profile Setup</span>
            <span style="color: {COLORS['neutral_500']};">50% Complete</span>
        </div>
        <div style="background: {COLORS['neutral_100']}; height: 8px; border-radius: 4px;">
            <div style="background: linear-gradient(90deg, {COLORS['primary']}, {COLORS['secondary']}); height: 100%; width: 50%; border-radius: 4px;"></div>
        </div>
    </div>
    """, unsafe_allow_html=True)

    with st.form("registration_form", clear_on_submit=False):
        # Personal Information
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1.5rem;">Personal Information</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            name = st.text_input(
                "Full Name *",
                placeholder="e.g., Priya Sharma",
                help="This will be visible to your matches"
            )
            email = st.text_input(
                "Email Address *",
                placeholder="<EMAIL>",
                help="We'll send you match notifications here"
            )

        with col2:
            location = st.text_input(
                "Location (Optional)",
                placeholder="e.g., Mumbai, India",
                help="Helps find local learning partners"
            )
            experience_level = st.selectbox(
                "Professional Experience",
                ["Student", "0-2 years", "2-5 years", "5-10 years", "10+ years"],
                index=2
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Skills Section
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1.5rem;">Skills Exchange</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("#### What You Can Teach")
            skill_have = st.text_input(
                "Your Expertise *",
                placeholder="e.g., React Development, Digital Marketing",
                help="Be specific for better matches"
            )

            skill_level_teach = st.selectbox(
                "Your Experience Level",
                ["Beginner (1-2 years)", "Intermediate (2-5 years)", "Advanced (5+ years)", "Expert (Professional)"],
                index=1
            )

        with col2:
            st.markdown("#### What You Want to Learn")
            skill_want = st.text_input(
                "Skill You Want to Learn *",
                placeholder="e.g., UI/UX Design, Content Writing",
                help="What skill would help you grow professionally?"
            )

            skill_level_learn = st.selectbox(
                "Your Current Level",
                ["Complete Beginner", "Some Knowledge", "Intermediate", "Need Advanced Help"],
                index=0
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Availability Section
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 1.5rem;">Availability & Preferences</h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            availability = st.selectbox(
                "When Can You Meet? *",
                [
                    "Weekends Only",
                    "Weekday Evenings (6-9 PM)",
                    "Flexible Schedule",
                    "Weekday Mornings",
                    "Custom Schedule"
                ]
            )

            if availability == "Custom Schedule":
                custom_availability = st.text_input(
                    "Specify Your Availability",
                    placeholder="e.g., Tuesdays and Thursdays 7-9 PM"
                )

        with col2:
            commitment = st.radio(
                "Time Commitment (per week)",
                ["1-2 hours", "3-5 hours", "6-10 hours", "10+ hours"],
                horizontal=True
            )

            learning_goal = st.text_area(
                "Learning Goals (Optional)",
                placeholder="e.g., I want to transition to a UX role or start freelancing",
                height=100
            )

        st.markdown("</div>", unsafe_allow_html=True)

        # Terms and Submit
        col1, col2, col3 = st.columns([1, 2, 1])
        with col2:
            terms_agreed = st.checkbox(
                "I agree to the Terms of Service and Community Guidelines",
                help="By joining, you commit to respectful learning and teaching"
            )

            submitted = st.form_submit_button(
                "Continue to Payment",
                type="primary",
                use_container_width=True
            )

        if submitted:
            if not all([name, email, skill_have, skill_want, availability, terms_agreed]):
                st.error("Please fill in all required fields and agree to the terms.")
            elif "@" not in email:
                st.error("Please enter a valid email address.")
            else:
                # Store user data
                final_availability = custom_availability if availability == "Custom Schedule" else availability

                st.session_state.user_data = {
                    'name': name,
                    'email': email,
                    'location': location,
                    'experience_level': experience_level,
                    'skill_have': skill_have,
                    'skill_want': skill_want,
                    'skill_level_teach': skill_level_teach,
                    'skill_level_learn': skill_level_learn,
                    'availability': final_availability,
                    'commitment': commitment,
                    'learning_goal': learning_goal,
                    'bio': f"Hi! I'm {name}. I can teach {skill_have} and I'm eager to learn {skill_want}. {learning_goal}"
                }

                # Simulate payment success and go to dashboard
                st.success("🎉 Registration successful! Welcome to SkillSwap!")
                st.session_state.authenticated = True
                st.session_state.is_premium = True
                time.sleep(2)
                st.session_state.page = "dashboard"
                st.rerun()

# Login Page
def show_login_page():
    # Back button
    if st.button("← Back to Home", key="back_home_login"):
        st.session_state.page = "home"
        st.rerun()

    st.markdown('<h2 class="section-header">Welcome Back!</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Enter your email to access your matches</p>', unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        st.markdown(f"""
        <div class="card">
            <h3 style="color: {COLORS['neutral_900']}; text-align: center; margin-bottom: 2rem;">Login to Your Account</h3>
        """, unsafe_allow_html=True)

        with st.form("login_form"):
            email = st.text_input(
                "Email Address",
                placeholder="Enter your registered email",
                help="We'll find your account and show your matches"
            )

            submitted = st.form_submit_button(
                "Access My Matches",
                type="primary",
                use_container_width=True
            )

            if submitted:
                if not email:
                    st.error("Please enter your email address.")
                elif "@" not in email:
                    st.error("Please enter a valid email address.")
                else:
                    # Simulate login success
                    st.session_state.user_data = {
                        'name': 'Demo User',
                        'email': email,
                        'skill_have': 'React Development',
                        'skill_want': 'UI/UX Design'
                    }
                    st.session_state.authenticated = True
                    st.session_state.is_premium = True
                    st.success("Login successful! Redirecting to your dashboard...")
                    time.sleep(1)
                    st.session_state.page = "dashboard"
                    st.rerun()

        st.markdown("</div>", unsafe_allow_html=True)

        # Help section
        st.markdown(f"""
        <div style="text-align: center; margin-top: 2rem;">
            <p style="color: {COLORS['neutral_500']};">
                Don't have an account?
                <a href="#" style="color: {COLORS['primary']}; text-decoration: none;">Start Your Journey</a>
            </p>
        </div>
        """, unsafe_allow_html=True)

# Dashboard with Navigation
def show_dashboard():
    user_data = st.session_state.user_data

    # Navigation
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        if st.button("🏠 Dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()
    with col2:
        if st.button("🎯 My Matches", use_container_width=True):
            st.session_state.page = "matches"
            st.rerun()
    with col3:
        if st.button("👤 Profile", use_container_width=True):
            st.session_state.page = "profile"
            st.rerun()
    with col4:
        if st.button("💳 Subscription", use_container_width=True):
            st.info("Subscription management coming soon!")
    with col5:
        if st.button("🚪 Logout", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.page = "home"
            st.rerun()

    # Welcome section
    st.markdown(f"""
    <div class="card" style="background: linear-gradient(135deg, {COLORS['primary']}, {COLORS['secondary']}); color: white;">
        <h2 style="margin-bottom: 0.5rem;">Welcome back, {user_data.get('name', 'Friend')}! 👋</h2>
        <p style="opacity: 0.9; margin: 0;">Ready to connect with your learning partners?</p>
    </div>
    """, unsafe_allow_html=True)

    # Quick stats
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.markdown(f"""
        <div class="stat-card">
            <span class="stat-number">15</span>
            <div class="stat-label">Available Matches</div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        st.markdown(f"""
        <div class="stat-card">
            <span class="stat-number">3</span>
            <div class="stat-label">Active Conversations</div>
        </div>
        """, unsafe_allow_html=True)

    with col3:
        st.markdown(f"""
        <div class="stat-card">
            <span class="stat-number">2</span>
            <div class="stat-label">Skills Learned</div>
        </div>
        """, unsafe_allow_html=True)

    with col4:
        st.markdown(f"""
        <div class="stat-card">
            <span class="stat-number">95%</span>
            <div class="stat-label">Match Success Rate</div>
        </div>
        """, unsafe_allow_html=True)

    # Recent activity and quick actions
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown('<h3 class="section-header">Recent Activity</h3>', unsafe_allow_html=True)

        activities = [
            {"type": "match", "text": "New match found: Arjun wants to learn React", "time": "2 hours ago"},
            {"type": "message", "text": "Priya sent you a message about UI/UX session", "time": "1 day ago"},
            {"type": "session", "text": "Completed session with Rohit on Digital Marketing", "time": "3 days ago"}
        ]

        for activity in activities:
            icon = "🎯" if activity['type'] == 'match' else "💬" if activity['type'] == 'message' else "✅"
            st.markdown(f"""
            <div class="card" style="padding: 1rem; margin-bottom: 0.5rem;">
                <div style="display: flex; align-items: center;">
                    <span style="margin-right: 1rem; font-size: 1.2rem;">{icon}</span>
                    <div style="flex: 1;">
                        <p style="margin: 0; color: {COLORS['neutral_900']};">{activity['text']}</p>
                        <small style="color: {COLORS['neutral_500']};">{activity['time']}</small>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)

    with col2:
        st.markdown('<h3 class="section-header">Quick Actions</h3>', unsafe_allow_html=True)

        if st.button("🔍 Find New Matches", use_container_width=True):
            st.session_state.page = "matches"
            st.rerun()

        if st.button("📝 Update Profile", use_container_width=True):
            st.session_state.page = "profile"
            st.rerun()

        if st.button("💬 Send Message", use_container_width=True):
            st.info("Messaging feature coming soon!")

        if st.button("📊 View Analytics", use_container_width=True):
            st.info("Analytics dashboard coming soon!")

# Matches Page with Social Feel
def show_matches_page():
    user_data = st.session_state.user_data

    # Navigation
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        if st.button("🏠 Dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()
    with col2:
        if st.button("🎯 My Matches", use_container_width=True):
            st.session_state.page = "matches"
            st.rerun()
    with col3:
        if st.button("👤 Profile", use_container_width=True):
            st.session_state.page = "profile"
            st.rerun()
    with col4:
        if st.button("💳 Subscription", use_container_width=True):
            st.info("Subscription management coming soon!")
    with col5:
        if st.button("🚪 Logout", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.page = "home"
            st.rerun()

    st.markdown('<h2 class="section-header">Your Perfect Matches</h2>', unsafe_allow_html=True)
    st.markdown(f'<p class="section-subtitle">People who want to learn <strong>{user_data.get("skill_have", "your skill")}</strong> and can teach <strong>{user_data.get("skill_want", "what you want")}</strong></p>', unsafe_allow_html=True)

    # Filter options
    col1, col2, col3 = st.columns(3)
    with col1:
        skill_filter = st.selectbox("Filter by Skill", ["All Skills", "React", "UI/UX", "Digital Marketing", "Python"])
    with col2:
        availability_filter = st.selectbox("Availability", ["All Times", "Weekends", "Evenings", "Flexible"])
    with col3:
        experience_filter = st.selectbox("Experience Level", ["All Levels", "Beginner", "Intermediate", "Advanced"])

    # Sample matches with enhanced profiles
    matches = [
        {
            "name": "Arjun Patel",
            "avatar": "AP",
            "skill_have": "UI/UX Design",
            "skill_want": "React Development",
            "experience": "3 years",
            "availability": "Weekday Evenings",
            "location": "Mumbai, India",
            "bio": "Product designer at a fintech startup. Love creating user-centered designs and want to learn frontend development to better collaborate with developers.",
            "match_score": 95,
            "verified": True,
            "rating": 4.9,
            "exchanges": 5,
            "skills_taught": ["Figma", "User Research", "Prototyping"],
            "learning_goals": "Build full-stack applications"
        },
        {
            "name": "Priya Sharma",
            "avatar": "PS",
            "skill_have": "Content Writing",
            "skill_want": "React Development",
            "experience": "4 years",
            "availability": "Flexible Schedule",
            "location": "Delhi, India",
            "bio": "Freelance content writer specializing in tech and SaaS. Want to transition into tech by learning React and building my own projects.",
            "match_score": 88,
            "verified": True,
            "rating": 4.8,
            "exchanges": 3,
            "skills_taught": ["Technical Writing", "SEO", "Content Strategy"],
            "learning_goals": "Career transition to tech"
        }
    ]

    for match in matches:
        st.markdown(f"""
        <div class="profile-card">
            <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1.5rem;">
                <div style="display: flex; align-items: center;">
                    <div class="profile-avatar" style="margin-right: 1rem;">
                        {match['avatar']}
                    </div>
                    <div>
                        <h3 style="color: {COLORS['primary']}; margin: 0; display: flex; align-items: center;">
                            {match['name']}
                            {'<span class="badge badge-success" style="margin-left: 0.5rem;">✓ Verified</span>' if match['verified'] else ''}
                        </h3>
                        <div style="color: {COLORS['neutral_500']}; font-size: 0.9rem; margin-top: 0.25rem;">
                            ⭐ {match['rating']} • {match['exchanges']} successful exchanges • {match['location']}
                        </div>
                    </div>
                </div>
                <div style="text-align: right;">
                    <div style="background: {COLORS['success']}; color: white; padding: 0.25rem 0.75rem;
                         border-radius: 20px; font-size: 0.875rem; font-weight: 600;">
                        {match['match_score']}% Match
                    </div>
                </div>
            </div>

            <div style="margin: 1.5rem 0;">
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                    <div>
                        <strong style="color: {COLORS['secondary']};">Can Teach:</strong><br>
                        <span style="color: {COLORS['neutral_900']};">{match['skill_have']} ({match['experience']})</span><br>
                        <small style="color: {COLORS['neutral_500']};">Skills: {', '.join(match['skills_taught'])}</small>
                    </div>
                    <div>
                        <strong style="color: {COLORS['primary']};">Wants to Learn:</strong><br>
                        <span style="color: {COLORS['neutral_900']};">{match['skill_want']}</span><br>
                        <small style="color: {COLORS['neutral_500']};">Goal: {match['learning_goals']}</small>
                    </div>
                </div>

                <div style="margin: 1.5rem 0;">
                    <strong>About:</strong><br>
                    <span style="color: {COLORS['neutral_900']}; line-height: 1.6;">{match['bio']}</span>
                </div>

                <div style="color: {COLORS['neutral_500']}; font-size: 0.9rem; margin-bottom: 1.5rem;">
                    📅 Available: {match['availability']}
                </div>
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 0.5rem;">
                <button class="btn-primary" style="padding: 0.75rem 1rem; margin-bottom: 0;">
                    💬 Start Chat
                </button>
                <button class="btn-secondary" style="padding: 0.75rem 1rem; margin-bottom: 0;">
                    👀 View Profile
                </button>
                <button style="background: {COLORS['accent']}; color: white; border: none;
                       padding: 0.75rem 1rem; border-radius: 8px; font-weight: 500; cursor: pointer;">
                    ⭐ Save Match
                </button>
            </div>
        </div>
        """, unsafe_allow_html=True)

# Profile Page
def show_profile_page():
    user_data = st.session_state.user_data

    # Navigation
    col1, col2, col3, col4, col5 = st.columns(5)
    with col1:
        if st.button("🏠 Dashboard", use_container_width=True):
            st.session_state.page = "dashboard"
            st.rerun()
    with col2:
        if st.button("🎯 My Matches", use_container_width=True):
            st.session_state.page = "matches"
            st.rerun()
    with col3:
        if st.button("👤 Profile", use_container_width=True):
            st.session_state.page = "profile"
            st.rerun()
    with col4:
        if st.button("💳 Subscription", use_container_width=True):
            st.info("Subscription management coming soon!")
    with col5:
        if st.button("🚪 Logout", use_container_width=True):
            st.session_state.authenticated = False
            st.session_state.page = "home"
            st.rerun()

    st.markdown('<h2 class="section-header">My Profile</h2>', unsafe_allow_html=True)
    st.markdown('<p class="section-subtitle">Manage your skills and preferences</p>', unsafe_allow_html=True)

    col1, col2 = st.columns([1, 2])

    with col1:
        # Profile summary
        st.markdown(f"""
        <div class="card" style="text-align: center;">
            <div class="profile-avatar" style="margin: 0 auto 1rem auto;">
                {user_data.get('name', 'U')[0].upper()}
            </div>
            <h3 style="color: {COLORS['neutral_900']}; margin-bottom: 0.5rem;">{user_data.get('name', 'User')}</h3>
            <p style="color: {COLORS['neutral_500']}; margin-bottom: 1rem;">{user_data.get('email', '<EMAIL>')}</p>
            <span class="badge badge-success">✓ Verified</span>
            <span class="badge badge-primary">Premium Member</span>
        </div>
        """, unsafe_allow_html=True)

        # Quick stats
        st.markdown(f"""
        <div class="card">
            <h4 style="color: {COLORS['neutral_900']}; margin-bottom: 1rem;">Your Stats</h4>
            <div style="margin-bottom: 0.5rem;">
                <strong>Profile Views:</strong> 127
            </div>
            <div style="margin-bottom: 0.5rem;">
                <strong>Matches Found:</strong> 15
            </div>
            <div style="margin-bottom: 0.5rem;">
                <strong>Skills Taught:</strong> 2
            </div>
            <div style="margin-bottom: 0.5rem;">
                <strong>Member Since:</strong> Jan 2024
            </div>
        </div>
        """, unsafe_allow_html=True)

    with col2:
        # Profile details
        st.markdown(f"""
        <div class="card">
            <h4 style="color: {COLORS['neutral_900']}; margin-bottom: 1.5rem;">Profile Information</h4>
            <div style="margin-bottom: 1rem;">
                <strong>Can Teach:</strong> {user_data.get('skill_have', 'Not specified')}
            </div>
            <div style="margin-bottom: 1rem;">
                <strong>Wants to Learn:</strong> {user_data.get('skill_want', 'Not specified')}
            </div>
            <div style="margin-bottom: 1rem;">
                <strong>Availability:</strong> {user_data.get('availability', 'Not specified')}
            </div>
            <div style="margin-bottom: 1rem;">
                <strong>Location:</strong> {user_data.get('location', 'Not specified')}
            </div>
            <div style="margin-bottom: 1rem;">
                <strong>Experience Level:</strong> {user_data.get('experience_level', 'Not specified')}
            </div>
        </div>
        """, unsafe_allow_html=True)

        # Edit profile button
        if st.button("✏️ Edit Profile", use_container_width=True):
            st.info("Profile editing feature coming soon!")

if __name__ == "__main__":
    main()
